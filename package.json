{"name": "cashboost-comparison", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "analyze": "ANALYZE=true npm run build", "build:analyze": "npm run analyze", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lighthouse": "lighthouse http://localhost:3000 --output html --output-path ./lighthouse-report.html", "perf": "npm run build && npm run lighthouse", "clean": "rm -rf .next out", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\""}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@types/node": "^22.10.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "autoprefixer": "^10.4.20", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "lucide-react": "^0.519.0", "next": "^15.3.4", "postcss": "^8.5.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.13.0", "@typescript-eslint/parser": "^8.13.0", "eslint": "^9.14.0", "eslint-config-next": "^15.0.3", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8"}, "engines": {"node": ">=20.0.0"}}