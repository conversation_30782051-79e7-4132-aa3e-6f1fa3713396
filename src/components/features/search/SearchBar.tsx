'use client'

import { But<PERSON> } from '@/components/ui'
import { useDebounce, useSearch } from '@/hooks'
import { cn } from '@/lib/utils'
import type { SearchFilters } from '@/types'
import {
  AdjustmentsHorizontalIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline'
import { AnimatePresence, motion } from 'framer-motion'
import React, { useEffect, useRef, useState } from 'react'

interface SearchBarProps {
  onSearch?: (query: string) => void
  onFiltersChange?: (filters: SearchFilters) => void
  placeholder?: string
  showFilters?: boolean
  className?: string
}

const categories = [
  'Moda & Vestuário',
  'Eletrônicos',
  'Casa & Decoração',
  'Marketplace',
  'Saúde & Beleza',
  'Esportes',
  'Livros & Educação',
  'Viagem & Turismo',
]

const platforms = ['Meliuz', 'Rakuten', 'TopCashback', 'Honey', 'Inter Shopping', 'Banco Pan']

export function SearchBar({
  onSearch,
  onFiltersChange,
  placeholder = 'Buscar lojas, categorias ou marcas...',
  showFilters = true,
  className,
}: SearchBarProps) {
  const { query, filters, updateQuery, updateFilters, clearSearch, isSearchActive } = useSearch()
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const debouncedQuery = useDebounce(query, 300)

  // Call external handlers
  useEffect(() => {
    if (onSearch) {
      onSearch(debouncedQuery)
    }
  }, [debouncedQuery, onSearch])

  useEffect(() => {
    if (onFiltersChange) {
      onFiltersChange(filters)
    }
  }, [filters, onFiltersChange])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateQuery(e.target.value)
  }

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    updateFilters({ [key]: value })
  }

  const handleClearAll = () => {
    clearSearch()
    setShowAdvancedFilters(false)
    inputRef.current?.focus()
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsFocused(false)
      inputRef.current?.blur()
    }
  }

  return (
    <div className={cn('relative mx-auto w-full max-w-2xl', className)}>
      {/* Main Search Input */}
      <div
        className={cn(
          'relative flex items-center rounded-2xl bg-white shadow-lg transition-all duration-300',
          isFocused ? 'shadow-xl ring-2 ring-emerald-500' : 'shadow-lg',
          'border border-gray-200 hover:border-emerald-300'
        )}
      >
        <div className="pointer-events-none absolute left-4 flex items-center">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
        </div>

        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="flex-1 bg-transparent py-4 pl-12 pr-4 text-lg text-gray-900 placeholder-gray-500 focus:outline-none"
        />

        <div className="flex items-center space-x-2 pr-4">
          {isSearchActive && (
            <motion.button
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              onClick={handleClearAll}
              className="p-1 text-gray-400 transition-colors hover:text-gray-600"
              aria-label="Limpar busca"
            >
              <XMarkIcon className="h-5 w-5" />
            </motion.button>
          )}

          {showFilters && (
            <Button
              variant={showAdvancedFilters ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className="flex items-center space-x-2"
            >
              <FunnelIcon className="h-4 w-4" />
              <span className="hidden sm:inline">Filtros</span>
            </Button>
          )}
        </div>
      </div>

      {/* Advanced Filters Panel */}
      <AnimatePresence>
        {showAdvancedFilters && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute left-0 right-0 top-full z-50 mt-2 rounded-2xl border border-gray-200 bg-white p-6 shadow-xl"
          >
            <div className="mb-6 flex items-center justify-between">
              <h3 className="flex items-center font-poppins text-lg font-bold text-gray-900">
                <AdjustmentsHorizontalIcon className="mr-2 h-5 w-5 text-emerald-600" />
                Filtros Avançados
              </h3>
              <button
                onClick={() => setShowAdvancedFilters(false)}
                className="p-1 text-gray-400 transition-colors hover:text-gray-600"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {/* Categories Filter */}
              <div>
                <label className="mb-3 block text-sm font-medium text-gray-700">Categorias</label>
                <div className="max-h-40 space-y-2 overflow-y-auto">
                  {categories.map(category => (
                    <label key={category} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.categories?.includes(category) || false}
                        onChange={e => {
                          const currentCategories = filters.categories || []
                          const newCategories = e.target.checked
                            ? [...currentCategories, category]
                            : currentCategories.filter(c => c !== category)
                          handleFilterChange(
                            'categories',
                            newCategories.length > 0 ? newCategories : undefined
                          )
                        }}
                        className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{category}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Platforms Filter */}
              <div>
                <label className="mb-3 block text-sm font-medium text-gray-700">Plataformas</label>
                <div className="max-h-40 space-y-2 overflow-y-auto">
                  {platforms.map(platform => (
                    <label key={platform} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.platforms?.includes(platform) || false}
                        onChange={e => {
                          const currentPlatforms = filters.platforms || []
                          const newPlatforms = e.target.checked
                            ? [...currentPlatforms, platform]
                            : currentPlatforms.filter(p => p !== platform)
                          handleFilterChange(
                            'platforms',
                            newPlatforms.length > 0 ? newPlatforms : undefined
                          )
                        }}
                        className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{platform}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Rate Range Filter */}
              <div>
                <label className="mb-3 block text-sm font-medium text-gray-700">
                  Taxa de Cashback
                </label>
                <div className="space-y-4">
                  <div>
                    <label className="mb-1 block text-xs text-gray-600">
                      Taxa mínima: {filters.minRate || 0}%
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="20"
                      step="0.5"
                      value={filters.minRate || 0}
                      onChange={e => handleFilterChange('minRate', parseFloat(e.target.value))}
                      className="slider h-2 w-full cursor-pointer appearance-none rounded-lg bg-gray-200"
                    />
                  </div>
                  <div>
                    <label className="mb-1 block text-xs text-gray-600">
                      Taxa máxima: {filters.maxRate || 20}%
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="20"
                      step="0.5"
                      value={filters.maxRate || 20}
                      onChange={e => handleFilterChange('maxRate', parseFloat(e.target.value))}
                      className="slider h-2 w-full cursor-pointer appearance-none rounded-lg bg-gray-200"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Options */}
            <div className="mt-6 border-t border-gray-200 pt-6">
              <div className="flex flex-wrap gap-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.featured || false}
                    onChange={e => handleFilterChange('featured', e.target.checked || undefined)}
                    className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Apenas em destaque</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.verified || false}
                    onChange={e => handleFilterChange('verified', e.target.checked || undefined)}
                    className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Apenas verificadas</span>
                </label>
              </div>
            </div>

            {/* Filter Actions */}
            <div className="mt-6 flex justify-between">
              <Button variant="outline" onClick={handleClearAll} className="text-gray-600">
                Limpar Filtros
              </Button>

              <Button onClick={() => setShowAdvancedFilters(false)}>Aplicar Filtros</Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
