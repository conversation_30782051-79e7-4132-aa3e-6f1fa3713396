import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui'
import { cn, formatPercentage, formatRelativeTime, getTrendColor } from '@/lib/utils'
import type { CashbackComparison } from '@/types'
import {
  ArrowDownIcon,
  ArrowTopRightOnSquareIcon,
  ArrowUpIcon,
  MinusIcon,
  ShieldCheckIcon,
  StarIcon,
} from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'
import { motion } from 'framer-motion'
import { memo } from 'react'

interface CashbackRateCardProps {
  comparison: CashbackComparison
  featured?: boolean
  compact?: boolean
  showPlatforms?: boolean
  className?: string
}

const TrendIcon = memo(({ trend, trendPercent }: { trend: string; trendPercent?: number }) => {
  const iconClass = cn('w-4 h-4', getTrendColor(trend))

  switch (trend) {
    case 'up':
      return <ArrowUpIcon className={iconClass} />
    case 'down':
      return <ArrowDownIcon className={iconClass} />
    case 'stable':
    default:
      return <MinusIcon className={iconClass} />
  }
})

TrendIcon.displayName = 'TrendIcon'

const TrustScore = memo(({ score }: { score: number }) => {
  const fullStars = Math.floor(score)
  const hasHalfStar = score % 1 >= 0.5

  return (
    <div className="flex items-center space-x-1">
      {Array.from({ length: 5 }, (_, i) => {
        if (i < fullStars) {
          return <StarIconSolid key={i} className="h-3 w-3 text-yellow-400" />
        } else if (i === fullStars && hasHalfStar) {
          return <StarIcon key={i} className="h-3 w-3 text-yellow-400" />
        } else {
          return <StarIcon key={i} className="h-3 w-3 text-gray-300" />
        }
      })}
      <span className="ml-1 text-xs text-gray-600">{score.toFixed(1)}</span>
    </div>
  )
})

TrustScore.displayName = 'TrustScore'

export const CashbackRateCard = memo(
  ({
    comparison,
    featured = false,
    compact = false,
    showPlatforms = true,
    className,
  }: CashbackRateCardProps) => {
    const { store, bestRate, rates, averageRate, totalPlatforms, lastUpdated } = comparison

    return (
      <Card
        variant={featured ? 'featured' : 'default'}
        className={cn('relative overflow-hidden', className)}
      >
        {featured && (
          <div className="absolute right-0 top-0 rounded-bl-lg bg-gradient-to-l from-emerald-500 to-emerald-400 px-3 py-1 text-xs font-bold text-white">
            DESTAQUE
          </div>
        )}

        <CardHeader>
          <div className="flex items-center space-x-3">
            <div
              className="flex h-12 w-12 items-center justify-center rounded-xl text-lg font-bold text-white shadow-lg"
              style={{ backgroundColor: store.brandColors.primary }}
            >
              {store.name.charAt(0)}
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="truncate font-poppins font-bold text-gray-900">{store.name}</h3>
              <p className="truncate text-sm text-gray-600">{store.category.name}</p>
            </div>
            {store.verified && (
              <ShieldCheckIcon className="h-5 w-5 flex-shrink-0 text-emerald-500" />
            )}
          </div>
        </CardHeader>

        <CardContent>
          {/* Best Rate Display */}
          <div className="mb-4 text-center">
            <div className="mb-2 flex items-center justify-center space-x-2">
              <span className="font-poppins text-3xl font-black text-emerald-600">
                {formatPercentage(bestRate.rate)}
              </span>
              <TrendIcon trend={bestRate.trend} trendPercent={bestRate.trendPercentage} />
            </div>
            <p className="text-sm text-gray-600">Melhor taxa de cashback</p>
            {bestRate.isPromotional && (
              <span className="mt-1 inline-block rounded-full bg-orange-100 px-2 py-1 text-xs font-medium text-orange-800">
                Oferta Promocional
              </span>
            )}
          </div>

          {/* Platform Information */}
          {showPlatforms && !compact && (
            <div className="mb-4 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Plataforma líder:</span>
                <span className="font-medium text-gray-900">{bestRate.platform.name}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Taxa média:</span>
                <span className="font-medium text-gray-900">{formatPercentage(averageRate)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Plataformas:</span>
                <span className="font-medium text-gray-900">{totalPlatforms} disponíveis</span>
              </div>
            </div>
          )}

          {/* Trust Score */}
          <div className="mb-4 flex items-center justify-between">
            <span className="text-sm text-gray-600">Confiabilidade:</span>
            <TrustScore score={store.trustScore} />
          </div>

          {/* Last Updated */}
          <div className="text-center text-xs text-gray-500">
            Atualizado {formatRelativeTime(lastUpdated)}
          </div>
        </CardContent>

        <CardFooter>
          <div className="flex space-x-2">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="flex flex-1 items-center justify-center space-x-2 rounded-lg bg-emerald-600 px-4 py-2 font-semibold text-white transition-colors duration-200 hover:bg-emerald-700"
            >
              <span>Ver Ofertas</span>
              <ArrowTopRightOnSquareIcon className="h-4 w-4" />
            </motion.button>

            {!compact && (
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="rounded-lg border border-emerald-200 px-4 py-2 text-emerald-600 transition-colors duration-200 hover:bg-emerald-50"
              >
                Comparar
              </motion.button>
            )}
          </div>
        </CardFooter>
      </Card>
    )
  }
)

CashbackRateCard.displayName = 'CashbackRateCard'
