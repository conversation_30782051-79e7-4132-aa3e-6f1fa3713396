'use client'

export function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="mb-6 flex items-center space-x-2">
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600">
                <span className="text-xl font-bold text-white">₵</span>
              </div>
              <span className="text-2xl font-bold text-white">CashBoost</span>
            </div>
            <p className="mb-6 leading-relaxed text-gray-300">
              A plataforma mais completa para comparar taxas de cashback no Brasil.
            </p>
          </div>

          {/* Links */}
          <div>
            <h3 className="mb-4 font-bold text-white">Plataforma</h3>
            <ul className="space-y-3">
              <li>
                <a href="#rates" className="text-gray-300 hover:text-emerald-400">
                  Taxas ao Vivo
                </a>
              </li>
              <li>
                <a href="#how-it-works" className="text-gray-300 hover:text-emerald-400">
                  Como Funciona
                </a>
              </li>
              <li>
                <a href="#stores" className="text-gray-300 hover:text-emerald-400">
                  Lojas
                </a>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="mb-4 font-bold text-white">Contato</h3>
            <ul className="space-y-3">
              <li className="text-gray-300"><EMAIL></li>
              <li className="text-gray-300">+55 11 99999-9999</li>
              <li className="text-gray-300">São Paulo, SP</li>
            </ul>
          </div>
        </div>

        {/* Bottom */}
        <div className="mt-16 border-t border-gray-800 pt-8 text-center">
          <p className="text-sm text-gray-400">© 2024 CashBoost. Todos os direitos reservados.</p>
        </div>
      </div>
    </footer>
  )
}
