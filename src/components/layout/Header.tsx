'use client'

import { Bars3Icon, MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline'
import { AnimatePresence, motion } from 'framer-motion'
import Link from 'next/link'
import { useEffect, useState } from 'react'

const navigation = [
  { name: 'Taxas ao Vivo', href: '#rates' },
  { name: 'Comparar Plata<PERSON>', href: '#platforms' },
  { name: 'Como Funciona', href: '#how-it-works' },
  { name: 'Todas as Lojas', href: '#stores' },
]

export function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToSection = (href: string) => {
    if (href.startsWith('#')) {
      const element = document.querySelector(href)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }
    setMobileMenuOpen(false)
  }

  return (
    <header
      className={`fixed left-0 right-0 top-0 z-50 transition-all duration-300 ${
        scrolled ? 'glass shadow-lg backdrop-blur-md' : 'bg-transparent'
      }`}
    >
      <nav className="container-responsive" aria-label="Global">
        <div className="flex h-16 items-center justify-between lg:h-20">
          {/* Logo */}
          <div className="flex lg:flex-1">
            <Link href="/" className="focus-visible -m-1.5 p-1.5">
              <span className="sr-only">CashBoost</span>
              <div className="flex items-center space-x-2">
                <div className="shadow-green-medium flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600 lg:h-10 lg:w-10">
                  <span className="text-lg font-bold text-white lg:text-xl">₵</span>
                </div>
                <span className="gradient-text font-poppins text-xl font-bold lg:text-2xl">
                  CashBoost
                </span>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex lg:gap-x-8">
            {navigation.map(item => (
              <button
                key={item.name}
                onClick={() => scrollToSection(item.href)}
                className="focus-visible text-sm font-semibold leading-6 text-gray-700 transition-colors duration-200 hover:text-emerald-600"
              >
                {item.name}
              </button>
            ))}
          </div>

          {/* Desktop CTA */}
          <div className="hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4">
            <button className="btn-secondary text-sm">
              <MagnifyingGlassIcon className="mr-2 h-4 w-4" />
              Buscar Lojas
            </button>
            <button className="btn-primary text-sm">Comparar Taxas</button>
          </div>

          {/* Mobile menu button */}
          <div className="flex lg:hidden">
            <button
              type="button"
              className="focus-visible -m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700 hover:text-emerald-600"
              onClick={() => setMobileMenuOpen(true)}
            >
              <span className="sr-only">Open main menu</span>
              <Bars3Icon className="h-6 w-6" aria-hidden="true" />
            </button>
          </div>
        </div>
      </nav>

      {/* Mobile menu */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-40 bg-black/20 backdrop-blur-sm lg:hidden"
              onClick={() => setMobileMenuOpen(false)}
            />

            {/* Menu Panel */}
            <motion.div
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10 lg:hidden"
            >
              <div className="flex items-center justify-between">
                <a href="/" className="-m-1.5 p-1.5">
                  <span className="sr-only">CashBoost</span>
                  <div className="flex items-center space-x-2">
                    <div className="shadow-green-medium flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600">
                      <span className="text-lg font-bold text-white">₵</span>
                    </div>
                    <span className="gradient-text font-poppins text-xl font-bold">CashBoost</span>
                  </div>
                </a>
                <button
                  type="button"
                  className="focus-visible -m-2.5 rounded-md p-2.5 text-gray-700 hover:text-emerald-600"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <span className="sr-only">Close menu</span>
                  <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                </button>
              </div>

              <div className="mt-6 flow-root">
                <div className="-my-6 divide-y divide-gray-500/10">
                  <div className="space-y-2 py-6">
                    {navigation.map(item => (
                      <button
                        key={item.name}
                        onClick={() => scrollToSection(item.href)}
                        className="focus-visible -mx-3 block w-full rounded-lg px-3 py-2 text-left text-base font-semibold leading-7 text-gray-900 transition-colors duration-200 hover:bg-emerald-50 hover:text-emerald-600"
                      >
                        {item.name}
                      </button>
                    ))}
                  </div>

                  <div className="space-y-4 py-6">
                    <button
                      className="btn-secondary w-full justify-center"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <MagnifyingGlassIcon className="mr-2 h-4 w-4" />
                      Buscar Lojas
                    </button>
                    <button
                      className="btn-primary w-full justify-center"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Comparar Taxas
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </header>
  )
}
