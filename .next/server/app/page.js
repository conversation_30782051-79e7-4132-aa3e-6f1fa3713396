(()=>{var e={};e.id=974,e.ids=[974],e.modules={149:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(3210);let i=n.forwardRef(function({title:e,titleId:t,...r},i){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"}))})},195:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return o},urlObjectKeys:function(){return s}});let n=r(4441)._(r(6715)),i=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",s=e.pathname||"",o=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||i.test(a))&&!1!==u?(u="//"+(u||""),s&&"/"!==s[0]&&(s="/"+s)):u||(u=""),o&&"#"!==o[0]&&(o="#"+o),c&&"?"!==c[0]&&(c="?"+c),""+a+u+(s=s.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+o}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function o(e){return a(e)}},593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NavigationResultTag:function(){return d},PrefetchPriority:function(){return h},cancelPrefetchTask:function(){return l},createCacheKey:function(){return c},getCurrentCacheVersion:function(){return s},navigate:function(){return i},prefetch:function(){return n},reschedulePrefetchTask:function(){return u},revalidateEntireCache:function(){return a},schedulePrefetchTask:function(){return o}});let r=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},n=r,i=r,a=r,s=r,o=r,l=r,u=r,c=r;var d=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),h=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},642:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return u},getSelectedParams:function(){return function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],a=Array.isArray(t),s=a?t[1]:t;!s||s.startsWith(i.PAGE_SEGMENT_KEY)||(a&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):a&&(r[t[0]]=t[1]),r=e(n,r))}return r}}});let n=r(2859),i=r(3913),a=r(4077),s=e=>"/"===e[0]?e.slice(1):e,o=e=>"string"==typeof e?"children"===e?"":e:e[1];function l(e){return e.reduce((e,t)=>""===(t=s(t))||(0,i.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===i.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(i.PAGE_SEGMENT_KEY))return"";let a=[o(r)],s=null!=(t=e[1])?t:{},c=s.children?u(s.children):void 0;if(void 0!==c)a.push(c);else for(let[e,t]of Object.entries(s)){if("children"===e)continue;let r=u(t);void 0!==r&&a.push(r)}return l(a)}function c(e,t){let r=function e(t,r){let[i,s]=t,[l,c]=r,d=o(i),h=o(l);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||h.startsWith(e)))return"";if(!(0,a.matchSegment)(i,l)){var f;return null!=(f=u(r))?f:""}for(let t in s)if(c[t]){let r=e(s[t],c[t]);if(null!==r)return o(l)+"/"+r}return null}(e,t);return null==r||"/"===r?r:l(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},922:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(3210);let i=n.forwardRef(function({title:e,titleId:t,...r},i){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))})},942:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(3210);let i=n.forwardRef(function({title:e,titleId:t,...r},i){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},1082:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(3210);let i=n.forwardRef(function({title:e,titleId:t,...r},i){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},1135:()=>{},1194:(e,t,r)=>{"use strict";r.d(t,{default:()=>g});var n=r(687),i=r(3210),a=r(6001),s=r(149);let o=i.forwardRef(function({title:e,titleId:t,...r},n){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"}))}),l=i.forwardRef(function({title:e,titleId:t,...r},n){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 12h14"}))}),u=i.forwardRef(function({title:e,titleId:t,...r},n){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"}))}),c=i.forwardRef(function({title:e,titleId:t,...r},n){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"}))}),d=i.forwardRef(function({title:e,titleId:t,...r},n){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{fillRule:"evenodd",d:"M12.516 2.17a.75.75 0 0 0-1.032 0 11.209 11.209 0 0 1-7.877 ********** 0 0 0-.722.515A12.74 12.74 0 0 0 2.25 9.75c0 5.942 4.064 10.933 9.563 12.348a.749.749 0 0 0 .374 0c5.499-1.415 9.563-6.406 9.563-12.348 0-1.39-.223-2.73-.635-3.985a.75.75 0 0 0-.722-.516l-.143.001c-2.996 0-5.717-1.17-7.734-3.08Zm3.094 8.016a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",clipRule:"evenodd"}))}),h=i.forwardRef(function({title:e,titleId:t,...r},n){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{fillRule:"evenodd",d:"M15.75 2.25H21a.75.75 0 0 1 .75.75v5.25a.75.75 0 0 1-1.5 0V4.81L8.03 17.03a.75.75 0 0 1-1.06-1.06L19.19 3.75h-3.44a.75.75 0 0 1 0-1.5Zm-10.5 4.5a1.5 1.5 0 0 0-1.5 1.5v10.5a1.5 1.5 0 0 0 1.5 1.5h10.5a1.5 1.5 0 0 0 1.5-1.5V10.5a.75.75 0 0 1 1.5 0v8.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V8.25a3 3 0 0 1 3-3h8.25a.75.75 0 0 1 0 1.5H5.25Z",clipRule:"evenodd"}))});function f({rate:e,featured:t=!1,getTrendIcon:r}){return(0,n.jsxs)(a.P.div,{whileHover:{y:-4,scale:1.02},transition:{duration:.2},className:`${t?"card-featured":"card"} p-6 relative overflow-hidden group cursor-pointer`,children:[t&&(0,n.jsx)("div",{className:"absolute top-4 right-4 bg-gradient-to-r from-yellow-400 to-orange-400 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg",children:"FEATURED"}),(0,n.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,n.jsx)("div",{className:"w-12 h-12 rounded-xl bg-gray-100 flex items-center justify-center shadow-sm",children:(0,n.jsx)("span",{className:"text-gray-600 font-bold text-lg",children:e.store.name.charAt(0)})}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,n.jsx)("h3",{className:"font-poppins font-bold text-base text-gray-900",children:e.store.name}),e.store.verified&&(0,n.jsx)(d,{className:"w-4 h-4 text-emerald-500"})]}),(0,n.jsx)("p",{className:"text-xs text-gray-500",children:e.store.category})]})]}),(0,n.jsx)("div",{className:"relative mb-4",children:(0,n.jsxs)("div",{className:"bg-gradient-to-r from-emerald-500 via-emerald-400 to-green-400 rounded-2xl p-4 text-center relative overflow-hidden",children:[(0,n.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[(0,n.jsx)("div",{className:"absolute top-2 right-2 w-8 h-8 bg-white rounded-full"}),(0,n.jsx)("div",{className:"absolute bottom-2 left-2 w-6 h-6 bg-white rounded-full"})]}),(0,n.jsxs)("div",{className:"relative z-10",children:[(0,n.jsxs)("div",{className:"text-3xl font-poppins font-black text-white mb-1",children:[e.bestRate,"%"]}),(0,n.jsx)("div",{className:"text-emerald-100 text-sm font-medium",children:"Melhor Taxa"})]}),(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 animate-shine"})]})}),(0,n.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,n.jsx)("h4",{className:"text-xs font-semibold text-gray-600 mb-3",children:"Principais Plataformas:"}),e.platforms.slice(0,2).map((e,t)=>(0,n.jsxs)("div",{className:"bg-gray-50 rounded-xl p-3 flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:`w-2 h-2 rounded-full ${0===t?"bg-emerald-500":"bg-gray-400"}`}),(0,n.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.name})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsxs)("span",{className:"font-bold text-emerald-600 text-sm",children:[e.rate,"%"]}),r(e.trend,e.trendPercent)]})]},e.name))]}),(0,n.jsx)("div",{className:"text-center text-xs text-gray-500 mb-4",children:(0,n.jsxs)("span",{children:["Atualizado ",e.lastUpdated," • ",e.platforms.length," plataformas"]})}),(0,n.jsxs)(a.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 text-sm shadow-lg hover:shadow-xl",children:["Ver Todas as Taxas",(0,n.jsx)(h,{className:"w-4 h-4 ml-1 inline"})]}),(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-emerald-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none rounded-2xl"})]})}let p=[{id:"1",store:{id:"nike",name:"Nike",logo:"/logos/nike.svg",category:"Moda & Vestu\xe1rio",verified:!0,trustScore:4.8},bestRate:8.5,platforms:[{name:"Meliuz",rate:8.5,trend:"up",trendPercent:15},{name:"Rakuten",rate:7,trend:"stable"},{name:"TopCashback",rate:6.5,trend:"down",trendPercent:5}],featured:!0,lastUpdated:"h\xe1 2 horas"},{id:"2",store:{id:"amazon",name:"Amazon",logo:"/logos/amazon.svg",category:"Marketplace",verified:!0,trustScore:4.9},bestRate:5.5,platforms:[{name:"Inter Shopping",rate:5.5,trend:"up",trendPercent:10},{name:"Rakuten",rate:4.8,trend:"stable"},{name:"Meliuz",rate:4.2,trend:"up",trendPercent:8}],featured:!0,lastUpdated:"h\xe1 1 hora"},{id:"3",store:{id:"target",name:"Target",logo:"/logos/target.svg",category:"Loja de Departamento",verified:!0,trustScore:4.7},bestRate:4.2,platforms:[{name:"Banco Pan",rate:4.2,trend:"up",trendPercent:20},{name:"Meliuz",rate:3.8,trend:"stable"},{name:"Inter Shopping",rate:3.5,trend:"down",trendPercent:3}],featured:!1,lastUpdated:"h\xe1 3 horas"},{id:"4",store:{id:"bestbuy",name:"Best Buy",logo:"/logos/bestbuy.svg",category:"Eletr\xf4nicos",verified:!0,trustScore:4.6},bestRate:4,platforms:[{name:"TopCashback",rate:4,trend:"stable"},{name:"Rakuten",rate:3.5,trend:"up",trendPercent:12},{name:"Honey",rate:3.2,trend:"stable"}],featured:!1,lastUpdated:"h\xe1 4 horas"},{id:"5",store:{id:"walmart",name:"Walmart",logo:"/logos/walmart.svg",category:"Loja de Departamento",verified:!0,trustScore:4.5},bestRate:3.8,platforms:[{name:"TopCashback",rate:3.8,trend:"up",trendPercent:18},{name:"BeFrugal",rate:3.2,trend:"stable"},{name:"Rakuten",rate:2.8,trend:"down",trendPercent:7}],featured:!1,lastUpdated:"h\xe1 2 horas"},{id:"6",store:{id:"macys",name:"Macy's",logo:"/logos/macys.svg",category:"Moda & Vestu\xe1rio",verified:!0,trustScore:4.4},bestRate:6.2,platforms:[{name:"Rakuten",rate:6.2,trend:"up",trendPercent:25},{name:"TopCashback",rate:5.8,trend:"stable"},{name:"BeFrugal",rate:5.5,trend:"up",trendPercent:10}],featured:!1,lastUpdated:"h\xe1 1 hora"}],m=["Todas","Moda & Vestu\xe1rio","Eletr\xf4nicos","Loja de Departamento","Marketplace"];function g(){let[e,t]=(0,i.useState)("Todas"),[r,d]=(0,i.useState)("rate"),h=p.filter(t=>"Todas"===e||t.store.category===e).sort((e,t)=>{switch(r){case"rate":return t.bestRate-e.bestRate;case"name":return e.store.name.localeCompare(t.store.name);case"updated":return new Date(t.lastUpdated).getTime()-new Date(e.lastUpdated).getTime();default:return 0}}),g=(e,t)=>{switch(e){case"up":return(0,n.jsx)(s.A,{className:"w-4 h-4 text-emerald-500"});case"down":return(0,n.jsx)(o,{className:"w-4 h-4 text-red-500"});default:return(0,n.jsx)(l,{className:"w-4 h-4 text-gray-400"})}};return(0,n.jsx)("section",{id:"rates",className:"py-responsive bg-white",children:(0,n.jsxs)("div",{className:"container-responsive",children:[(0,n.jsxs)("div",{className:"text-center mb-16",children:[(0,n.jsxs)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"inline-flex items-center px-4 py-2 rounded-full bg-emerald-50 text-emerald-700 font-medium text-sm mb-6",children:[(0,n.jsx)(s.A,{className:"w-4 h-4 mr-2"}),"Taxas de Cashback ao Vivo"]}),(0,n.jsxs)(a.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1},viewport:{once:!0},className:"text-responsive-2xl font-poppins font-bold text-gray-900 mb-4",children:["Compara\xe7\xe3o de Taxas"," ",(0,n.jsx)("span",{className:"gradient-text",children:"ao Vivo"})]}),(0,n.jsx)(a.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"text-responsive-sm text-gray-600 max-w-2xl mx-auto",children:"Compare taxas de cashback instantaneamente entre plataformas brasileiras e internacionais. Veja qual plataforma oferece a melhor taxa para cada loja - sem necessidade de cadastro."})]}),(0,n.jsxs)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},className:"flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6 mb-12",children:[(0,n.jsx)("div",{className:"flex flex-wrap gap-2",children:m.map(r=>(0,n.jsx)("button",{onClick:()=>t(r),className:`px-4 py-2 rounded-xl font-medium text-sm transition-all duration-200 ${e===r?"bg-emerald-500 text-white shadow-green-soft":"bg-gray-100 text-gray-600 hover:bg-gray-200"}`,children:r},r))}),(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsx)(u,{className:"w-5 h-5 text-gray-400"}),(0,n.jsxs)("select",{value:r,onChange:e=>d(e.target.value),className:"px-4 py-2 rounded-xl border border-gray-200 text-sm font-medium text-gray-700 focus:border-emerald-500 focus:ring-2 focus:ring-emerald-100 outline-none",children:[(0,n.jsx)("option",{value:"rate",children:"Maior Taxa"}),(0,n.jsx)("option",{value:"name",children:"Nome da Loja"}),(0,n.jsx)("option",{value:"updated",children:"Rec\xe9m Atualizado"})]})]})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:h.map((e,t)=>(0,n.jsx)(a.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},children:(0,n.jsx)(f,{rate:e,featured:e.featured,getTrendIcon:g})},e.id))}),(0,n.jsx)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.5},viewport:{once:!0},className:"text-center mt-12",children:(0,n.jsxs)("button",{className:"btn-primary text-lg px-8 py-4",children:["Comparar Todas as 500+ Lojas",(0,n.jsx)(c,{className:"w-5 h-5 ml-2"})]})})]})})}},1204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var n=r(7413),i=r(7549),a=r(9934),s=r(4712),o=r(8926),l=r(4459),u=r(8949);function c(){return(0,n.jsxs)("main",{className:"min-h-screen",children:[(0,n.jsx)(o.Header,{}),(0,n.jsx)(i.default,{}),(0,n.jsx)(l.default,{}),(0,n.jsx)(a.default,{}),(0,n.jsx)(u.default,{}),(0,n.jsx)(s.Footer,{})]})}},1279:(e,t,r)=>{"use strict";r.d(t,{t:()=>n});let n=(0,r(3210).createContext)(null)},1499:(e,t,r)=>{"use strict";function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>n})},1500:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,a,s,o,l,u){if(0===Object.keys(s[1]).length){r.head=l;return}for(let c in s[1]){let d,h=s[1][c],f=h[0],p=(0,n.createRouterCacheKey)(f),m=null!==o&&void 0!==o[2][c]?o[2][c]:null;if(a){let n=a.parallelRoutes.get(c);if(n){let a,s=(null==u?void 0:u.kind)==="auto"&&u.status===i.PrefetchCacheEntryStatus.reusable,o=new Map(n),d=o.get(p);a=null!==m?{lazyData:null,rsc:m[1],prefetchRsc:null,head:null,prefetchHead:null,loading:m[3],parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),navigatedAt:t}:s&&d?{lazyData:d.lazyData,rsc:d.rsc,prefetchRsc:d.prefetchRsc,head:d.head,prefetchHead:d.prefetchHead,parallelRoutes:new Map(d.parallelRoutes),loading:d.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),loading:null,navigatedAt:t},o.set(p,a),e(t,a,d,h,m||null,l,u),r.parallelRoutes.set(c,o);continue}}if(null!==m){let e=m[1],r=m[3];d={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else d={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let g=r.parallelRoutes.get(c);g?g.set(p,d):r.parallelRoutes.set(c,new Map([[p,d]])),e(t,d,void 0,h,m,l,u)}}}});let n=r(3123),i=r(9154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1550:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},1794:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=r(9289),i=r(6736);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,i.hasBasePath)(r.pathname)}catch(e){return!1}}},2030:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],i=r[0];if(Array.isArray(n)&&Array.isArray(i)){if(n[0]!==i[0]||n[2]!==i[2])return!0}else if(n!==i)return!0;if(t[4])return!r[4];if(r[4])return!0;let a=Object.values(t[1])[0],s=Object.values(r[1])[0];return!a||!s||e(a,s)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2157:(e,t,r)=>{"use strict";r.d(t,{L:()=>n});let n=(0,r(3210).createContext)({})},2255:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let n=r(1550);function i(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},2308:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,r){let[n,i,,s]=t;for(let o in n.includes(a.PAGE_SEGMENT_KEY)&&"refresh"!==s&&(t[2]=r,t[3]="refresh"),i)e(i[o],r)}},refreshInactiveParallelSegments:function(){return s}});let n=r(6928),i=r(9008),a=r(3913);async function s(e){let t=new Set;await o({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function o(e){let{navigatedAt:t,state:r,updatedTree:a,updatedCache:s,includeNextUrl:l,fetchedSegments:u,rootTree:c=a,canonicalUrl:d}=e,[,h,f,p]=a,m=[];if(f&&f!==d&&"refresh"===p&&!u.has(f)){u.add(f);let e=(0,i.fetchServerResponse)(new URL(f,location.origin),{flightRouterState:[c[0],c[1],c[2],"refetch"],nextUrl:l?r.nextUrl:null}).then(e=>{let{flightData:r}=e;if("string"!=typeof r)for(let e of r)(0,n.applyFlightData)(t,s,s,e)});m.push(e)}for(let e in h){let n=o({navigatedAt:t,state:r,updatedTree:h[e],updatedCache:s,includeNextUrl:l,fetchedSegments:u,rootTree:c,canonicalUrl:d});m.push(n)}await Promise.all(m)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2582:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n});let n=(0,r(3210).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},2708:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},2743:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(3210);let i=r(7044).B?n.useLayoutEffect:n.useEffect},2789:(e,t,r)=>{"use strict";r.d(t,{M:()=>i});var n=r(3210);function i(e){let t=(0,n.useRef)(null);return null===t.current&&(t.current=e()),t.current}},2974:(e,t,r)=>{Promise.resolve().then(r.bind(r,7549)),Promise.resolve().then(r.bind(r,9934)),Promise.resolve().then(r.bind(r,4712)),Promise.resolve().then(r.bind(r,8926)),Promise.resolve().then(r.bind(r,4459)),Promise.resolve().then(r.bind(r,8949))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3038:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let n=r(3210);function i(e,t){let r=(0,n.useRef)(null),i=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(r.current=a(e,n)),t&&(i.current=a(t,n))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3406:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{IDLE_LINK_STATUS:function(){return u},PENDING_LINK_STATUS:function(){return l},mountFormInstance:function(){return v},mountLinkInstance:function(){return y},onLinkVisibilityChanged:function(){return b},onNavigationIntent:function(){return w},pingVisibleLinks:function(){return P},setLinkForCurrentNavigation:function(){return c},unmountLinkForCurrentNavigation:function(){return d},unmountPrefetchableInstance:function(){return x}}),r(3690);let n=r(9752),i=r(9154),a=r(593),s=r(3210),o=null,l={pending:!0},u={pending:!1};function c(e){(0,s.startTransition)(()=>{null==o||o.setOptimisticLinkStatus(u),null==e||e.setOptimisticLinkStatus(l),o=e})}function d(e){o===e&&(o=null)}let h="function"==typeof WeakMap?new WeakMap:new Map,f=new Set,p="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;b(t.target,e)}},{rootMargin:"200px"}):null;function m(e,t){void 0!==h.get(e)&&x(e),h.set(e,t),null!==p&&p.observe(e)}function g(e){try{return(0,n.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function y(e,t,r,n,i,a){if(i){let i=g(t);if(null!==i){let t={router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:a};return m(e,t),t}}return{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:a}}function v(e,t,r,n){let i=g(t);null!==i&&m(e,{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:null})}function x(e){let t=h.get(e);if(void 0!==t){h.delete(e),f.delete(t);let r=t.prefetchTask;null!==r&&(0,a.cancelPrefetchTask)(r)}null!==p&&p.unobserve(e)}function b(e,t){let r=h.get(e);void 0!==r&&(r.isVisible=t,t?f.add(r):f.delete(r),j(r))}function w(e,t){let r=h.get(e);void 0!==r&&void 0!==r&&(r.wasHoveredOrTouched=!0,j(r))}function j(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,a.cancelPrefetchTask)(t);return}}function P(e,t){let r=(0,a.getCurrentCacheVersion)();for(let n of f){let s=n.prefetchTask;if(null!==s&&n.cacheVersion===r&&s.key.nextUrl===e&&s.treeAtTimeOfPrefetch===t)continue;null!==s&&(0,a.cancelPrefetchTask)(s);let o=(0,a.createCacheKey)(n.prefetchHref,e),l=n.wasHoveredOrTouched?a.PrefetchPriority.Intent:a.PrefetchPriority.Default;n.prefetchTask=(0,a.schedulePrefetchTask)(o,t,n.kind===i.PrefetchKind.FULL,l),n.cacheVersion=(0,a.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3690:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createMutableActionQueue:function(){return p},dispatchNavigateAction:function(){return y},dispatchTraverseAction:function(){return v},getCurrentAppRouterState:function(){return m},publicAppRouterInstance:function(){return x}});let n=r(9154),i=r(8830),a=r(3210),s=r(1992);r(593);let o=r(9129),l=r(6127),u=r(9752),c=r(5076),d=r(3406);function h(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?f({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:n.ACTION_REFRESH,origin:window.location.origin},t)))}async function f(e){let{actionQueue:t,action:r,setState:n}=e,i=t.state;t.pending=r;let a=r.payload,o=t.action(i,a);function l(e){r.discarded||(t.state=e,h(t,n),r.resolve(e))}(0,s.isThenable)(o)?o.then(l,e=>{h(t,n),r.reject(e)}):l(o)}function p(e,t){let r={state:e,dispatch:(e,t)=>(function(e,t,r){let i={resolve:r,reject:()=>{}};if(t.type!==n.ACTION_RESTORE){let e=new Promise((e,t)=>{i={resolve:e,reject:t}});(0,a.startTransition)(()=>{r(e)})}let s={payload:t,next:null,resolve:i.resolve,reject:i.reject};null===e.pending?(e.last=s,f({actionQueue:e,action:s,setState:r})):t.type===n.ACTION_NAVIGATE||t.type===n.ACTION_RESTORE?(e.pending.discarded=!0,s.next=e.pending.next,e.pending.payload.type===n.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),f({actionQueue:e,action:s,setState:r})):(null!==e.last&&(e.last.next=s),e.last=s)})(r,e,t),action:async(e,t)=>(0,i.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return r}function m(){return null}function g(){return null}function y(e,t,r,i){let a=new URL((0,l.addBasePath)(e),location.href);(0,d.setLinkForCurrentNavigation)(i);(0,o.dispatchAppRouterAction)({type:n.ACTION_NAVIGATE,url:a,isExternalUrl:(0,u.isExternalURL)(a),locationSearch:location.search,shouldScroll:r,navigateType:t,allowAliasing:!0})}function v(e,t){(0,o.dispatchAppRouterAction)({type:n.ACTION_RESTORE,url:new URL(e),tree:t})}let x={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let r=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),i=(0,u.createPrefetchURL)(e);if(null!==i){var a;(0,c.prefetchReducer)(r.state,{type:n.ACTION_PREFETCH,url:i,kind:null!=(a=null==t?void 0:t.kind)?a:n.PrefetchKind.FULL})}},replace:(e,t)=>{(0,a.startTransition)(()=>{var r;y(e,"replace",null==(r=null==t?void 0:t.scroll)||r,null)})},push:(e,t)=>{(0,a.startTransition)(()=>{var r;y(e,"push",null==(r=null==t?void 0:t.scroll)||r,null)})},refresh:()=>{(0,a.startTransition)(()=>{(0,o.dispatchAppRouterAction)({type:n.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3873:e=>{"use strict";e.exports=require("path")},3898:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillCacheWithNewSubTreeData:function(){return l},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return u}});let n=r(4400),i=r(1500),a=r(3123),s=r(3913);function o(e,t,r,o,l,u){let{segmentPath:c,seedData:d,tree:h,head:f}=o,p=t,m=r;for(let t=0;t<c.length;t+=2){let r=c[t],o=c[t+1],g=t===c.length-2,y=(0,a.createRouterCacheKey)(o),v=m.parallelRoutes.get(r);if(!v)continue;let x=p.parallelRoutes.get(r);x&&x!==v||(x=new Map(v),p.parallelRoutes.set(r,x));let b=v.get(y),w=x.get(y);if(g){if(d&&(!w||!w.lazyData||w===b)){let t=d[0],r=d[1],a=d[3];w={lazyData:null,rsc:u||t!==s.PAGE_SEGMENT_KEY?r:null,prefetchRsc:null,head:null,prefetchHead:null,loading:a,parallelRoutes:u&&b?new Map(b.parallelRoutes):new Map,navigatedAt:e},b&&u&&(0,n.invalidateCacheByRouterState)(w,b,h),u&&(0,i.fillLazyItemsTillLeafWithHead)(e,w,b,h,d,f,l),x.set(y,w)}continue}w&&b&&(w===b&&(w={lazyData:w.lazyData,rsc:w.rsc,prefetchRsc:w.prefetchRsc,head:w.head,prefetchHead:w.prefetchHead,parallelRoutes:new Map(w.parallelRoutes),loading:w.loading},x.set(y,w)),p=w,m=b)}}function l(e,t,r,n,i){o(e,t,r,n,i,!0)}function u(e,t,r,n,i){o(e,t,r,n,i,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4132:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},4364:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(3210);let i=n.forwardRef(function({title:e,titleId:t,...r},i){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"}))})},4397:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return i}});let n=r(3123);function i(e,t){return function e(t,r,i){if(0===Object.keys(r).length)return[t,i];let a=Object.keys(r).filter(e=>"children"!==e);for(let s of("children"in r&&a.unshift("children"),a)){let[a,o]=r[s],l=t.parallelRoutes.get(s);if(!l)continue;let u=(0,n.createRouterCacheKey)(a),c=l.get(u);if(!c)continue;let d=e(c,o,i+"/"+u);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4400:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return i}});let n=r(3123);function i(e,t,r){for(let i in r[1]){let a=r[1][i][0],s=(0,n.createRouterCacheKey)(a),o=t.parallelRoutes.get(i);if(o){let t=new Map(o);t.delete(s),e.parallelRoutes.set(i,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>l});var n=r(7413),i=r(396),a=r.n(i),s=r(5053),o=r.n(s);r(1135);let l={title:"CashBoost - Compare Taxas de Cashback e Maximize suas Economias",description:"Encontre as melhores taxas de cashback em todas as principais plataformas. Compare porcentagens de cashback em tempo real, descubra ofertas exclusivas e maximize suas economias com nossa ferramenta abrangente de compara\xe7\xe3o de cashback.",keywords:"compara\xe7\xe3o de cashback, taxas de cashback, compras online, economia, ofertas, plataformas de cashback, dinheiro de volta, recompensas de compras",authors:[{name:"Equipe CashBoost"}],creator:"CashBoost",publisher:"CashBoost",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("https://cashboost.com.br"),alternates:{canonical:"/"},openGraph:{title:"CashBoost - Compare Taxas de Cashback e Maximize suas Economias",description:"Encontre as melhores taxas de cashback em todas as principais plataformas. Compare porcentagens de cashback em tempo real e maximize suas economias.",url:"https://cashboost.com.br",siteName:"CashBoost",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"CashBoost - Cashback Comparison Platform"}],locale:"pt_BR",type:"website"},twitter:{card:"summary_large_image",title:"CashBoost - Compare Taxas de Cashback e Maximize suas Economias",description:"Encontre as melhores taxas de cashback em todas as principais plataformas. Compare porcentagens de cashback em tempo real e maximize suas economias.",images:["/og-image.jpg"],creator:"@cashboost"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function u({children:e}){return(0,n.jsxs)("html",{lang:"pt-BR",className:`${a().variable} ${o().variable}`,children:[(0,n.jsxs)("head",{children:[(0,n.jsx)("link",{rel:"icon",href:"/favicon.ico"}),(0,n.jsx)("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),(0,n.jsx)("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),(0,n.jsx)("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),(0,n.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,n.jsx)("meta",{name:"theme-color",content:"#10B981"}),(0,n.jsx)("meta",{name:"msapplication-TileColor",content:"#10B981"}),(0,n.jsx)("meta",{name:"application-name",content:"CashBoost"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-title",content:"CashBoost"}),(0,n.jsx)("meta",{name:"mobile-web-app-capable",content:"yes"}),(0,n.jsx)("meta",{name:"msapplication-config",content:"/browserconfig.xml"}),(0,n.jsx)("meta",{name:"msapplication-tap-highlight",content:"no"}),(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,n.jsx)("link",{rel:"dns-prefetch",href:"//www.googletagmanager.com"}),(0,n.jsx)("link",{rel:"dns-prefetch",href:"//fonts.googleapis.com"}),(0,n.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            `}})]}),(0,n.jsxs)("body",{className:"font-inter antialiased bg-green-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 overflow-x-hidden",children:[(0,n.jsx)("a",{href:"#main-content",className:"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-emerald-600 focus:text-white focus:rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2",children:"Pular para o conte\xfado principal"}),(0,n.jsx)("div",{className:"min-h-screen",children:e}),!1]})]})}},4459:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/workspace/novo/src/components/TopRatesSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/workspace/novo/src/components/TopRatesSection.tsx","default")},4479:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&null!==e}r.d(t,{G:()=>n})},4642:(e,t)=>{"use strict";function r(e){let t=parseInt(e.slice(0,2),16),r=t>>1&63,n=Array(6);for(let e=0;e<6;e++){let t=r>>5-e&1;n[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:n,hasRestArgs:1==(1&t)}}function n(e,t){let r=Array(e.length);for(let n=0;n<e.length;n++)(n<6&&t.usedArgs[n]||n>=6&&t.hasRestArgs)&&(r[n]=e[n]);return r}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{extractInfoFromServerReferenceId:function(){return r},omitUnusedArgs:function(){return n}})},4650:(e,t,r)=>{"use strict";r.d(t,{Footer:()=>C});var n=r(687),i=r(3210),a=r(6001);function s(...e){return function(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n);return i}(e))&&(n&&(n+=" "),n+=t);return n}(e)}let o={primary:"btn-primary",secondary:"btn-secondary",outline:"border-2 border-emerald-500 text-emerald-600 hover:bg-emerald-50",ghost:"text-emerald-600 hover:bg-emerald-50"},l={sm:"px-4 py-2 text-sm",md:"px-6 py-3 text-base",lg:"px-8 py-4 text-lg"};function u({variant:e="primary",size:t="md",loading:r=!1,className:i,children:u,disabled:c,...d}){return(0,n.jsxs)(a.P.button,{whileHover:{scale:c||r?1:1.02},whileTap:{scale:c||r?1:.98},className:s("inline-flex items-center justify-center font-semibold rounded-xl transition-all duration-300 focus-visible disabled:opacity-50 disabled:cursor-not-allowed",o[e],l[t],i),disabled:c||r,...d,children:[r&&(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),u]})}let c={default:"h-4 w-full",card:"h-48 w-full",text:"h-4",circle:"h-12 w-12 rounded-full"};i.Component;let d=i.forwardRef(function({title:e,titleId:t,...r},n){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"}))}),h=i.forwardRef(function({title:e,titleId:t,...r},n){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"}))}),f=i.forwardRef(function({title:e,titleId:t,...r},n){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 17.25v1.007a3 3 0 0 1-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0 1 15 18.257V17.25m6-12V15a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 15V5.25m18 0A2.25 2.25 0 0 0 18.75 3H5.25A2.25 2.25 0 0 0 3 5.25m18 0V12a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 12V5.25"}))}),p={theme:"cashboost-theme"},m={twitter:"https://twitter.com/cashboost",instagram:"https://instagram.com/cashboost",linkedin:"https://linkedin.com/company/cashboost"},g={email:"<EMAIL>",phone:"+55 11 99999-9999",address:"S\xe3o Paulo, SP - Brasil"},y={light:d,dark:h,system:f},v={light:"Claro",dark:"Escuro",system:"Sistema"};(0,i.memo)(({variant:e="button",className:t})=>{let{theme:r,toggleTheme:o,setTheme:l}=function(){let[e,t]=function(e,t){let[r,n]=(0,i.useState)(()=>t);return[r,e=>{try{let t=e instanceof Function?e(r):e;n(t)}catch(e){}}]}(p.theme,"system"),[r,n]=(0,i.useState)("light"),a=e=>{t(e)};return{theme:e,resolvedTheme:"system"===e?r:e,setTheme:a,toggleTheme:()=>{"light"===e?a("dark"):"dark"===e?a("system"):a("light")}}}();if("dropdown"===e)return(0,n.jsx)("div",{className:s("relative",t),children:(0,n.jsx)("select",{value:r,onChange:e=>l(e.target.value),className:"appearance-none bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",children:Object.entries(v).map(([e,t])=>(0,n.jsx)("option",{value:e,children:t},e))})});let u=y[r];return(0,n.jsx)(a.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:o,className:s("inline-flex items-center justify-center w-10 h-10 rounded-lg","bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700","text-gray-700 dark:text-gray-300 hover:text-emerald-600 dark:hover:text-emerald-400","transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500",t),"aria-label":`Alternar tema (atual: ${v[r]})`,title:`Tema atual: ${v[r]}`,children:(0,n.jsx)(u,{className:"w-5 h-5"})})}).displayName="ThemeToggle";let x={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"},b=i.forwardRef(function({title:e,titleId:t,...r},n){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))}),w=i.forwardRef(function({title:e,titleId:t,...r},n){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))}),j=i.forwardRef(function({title:e,titleId:t,...r},n){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))});var P=r(149);let T={sm:"max-w-3xl",md:"max-w-5xl",lg:"max-w-6xl",xl:"max-w-7xl",full:"max-w-full"};function E({size:e="xl",className:t,children:r,...i}){return(0,n.jsx)("div",{className:s("mx-auto px-4 sm:px-6 lg:px-8",T[e],t),...i,children:r})}let R={platform:{title:"Plataforma",links:[{name:"Como Funciona",href:"#how-it-works"},{name:"Comparar Taxas",href:"#rates"},{name:"Principais Lojas",href:"#stores"},{name:"Todas as Plataformas",href:"#platforms"},{name:"Ofertas Especiais",href:"#offers"}]},resources:{title:"Recursos",links:[{name:"Blog",href:"/blog"},{name:"Guia de Cashback",href:"/guide"},{name:"Alertas de Taxa",href:"/alerts"},{name:"Documenta\xe7\xe3o da API",href:"/api"},{name:"Central de Ajuda",href:"/help"}]},company:{title:"Empresa",links:[{name:"Sobre N\xf3s",href:"/about"},{name:"Carreiras",href:"/careers"},{name:"Imprensa",href:"/press"},{name:"Parcerias",href:"/partnerships"},{name:"Contato",href:"/contact"}]},legal:{title:"Legal",links:[{name:"Pol\xedtica de Privacidade",href:"/privacy"},{name:"Termos de Uso",href:"/terms"},{name:"Pol\xedtica de Cookies",href:"/cookies"},{name:"LGPD",href:"/lgpd"},{name:"Disclaimer",href:"/disclaimer"}]}},M={twitter:(0,n.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{d:"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"})}),instagram:(0,n.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.229 14.794 3.74 13.643 3.74 12.346s.49-2.448 1.386-3.323c.896-.875 2.026-1.297 3.323-1.297s2.448.422 3.323 1.297c.875.875 1.297 2.026 1.297 3.323s-.422 2.448-1.297 3.323c-.875.875-2.026 1.297-3.323 1.297z",clipRule:"evenodd"})}),linkedin:(0,n.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})};function C(){let e=e=>{e.startsWith("#")&&function(e){let t=document.getElementById(e);t&&t.scrollIntoView({behavior:"smooth"})}(e.substring(1))};return(0,n.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,n.jsxs)(E,{children:[(0,n.jsxs)("div",{className:"py-16",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-5 lg:gap-12",children:[(0,n.jsx)("div",{className:"lg:col-span-2",children:(0,n.jsxs)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},children:[(0,n.jsxs)("div",{className:"mb-6 flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"shadow-green-medium flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600",children:(0,n.jsx)("span",{className:"text-xl font-bold text-white",children:"₵"})}),(0,n.jsx)("span",{className:"font-poppins text-2xl font-bold text-white",children:"CashBoost"})]}),(0,n.jsx)("p",{className:"mb-6 leading-relaxed text-gray-300",children:"A plataforma mais completa para comparar taxas de cashback no Brasil. Maximize suas economias e nunca perca as melhores ofertas."}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3 text-gray-300",children:[(0,n.jsx)(b,{className:"h-5 w-5 text-emerald-400"}),(0,n.jsx)("span",{children:g.email})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-3 text-gray-300",children:[(0,n.jsx)(w,{className:"h-5 w-5 text-emerald-400"}),(0,n.jsx)("span",{children:g.phone})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-3 text-gray-300",children:[(0,n.jsx)(j,{className:"h-5 w-5 text-emerald-400"}),(0,n.jsx)("span",{children:g.address})]})]})]})}),Object.entries(R).map(([t,r],i)=>(0,n.jsxs)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*i},viewport:{once:!0},children:[(0,n.jsx)("h3",{className:"mb-4 font-poppins font-bold text-white",children:r.title}),(0,n.jsx)("ul",{className:"space-y-3",children:r.links.map(t=>(0,n.jsx)("li",{children:(0,n.jsx)("button",{onClick:()=>e(t.href),className:"text-left text-gray-300 transition-colors duration-200 hover:text-emerald-400",children:t.name})},t.name))})]},t))]}),(0,n.jsx)(a.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"mt-16 border-t border-gray-800 pt-8",children:(0,n.jsxs)("div",{className:"mx-auto max-w-md text-center lg:flex lg:max-w-none lg:items-center lg:justify-between lg:text-left",children:[(0,n.jsxs)("div",{className:"lg:flex-1",children:[(0,n.jsx)("h3",{className:"mb-2 font-poppins text-xl font-bold text-white",children:"Receba as Melhores Ofertas"}),(0,n.jsx)("p",{className:"text-gray-300",children:"Seja notificado sobre aumentos de taxa e ofertas exclusivas"})]}),(0,n.jsx)("div",{className:"mt-6 lg:ml-8 lg:mt-0",children:(0,n.jsxs)("div",{className:"flex flex-col gap-3 sm:flex-row",children:[(0,n.jsx)("input",{type:"email",placeholder:"Seu melhor e-mail",className:"flex-1 rounded-lg border border-gray-700 bg-gray-800 px-4 py-3 text-white placeholder-gray-400 focus:border-emerald-500 focus:outline-none focus:ring-2 focus:ring-emerald-500"}),(0,n.jsx)(u,{className:"whitespace-nowrap",children:"Inscrever-se"})]})})]})})]}),(0,n.jsx)("div",{className:"border-t border-gray-800 py-8",children:(0,n.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between",children:[(0,n.jsxs)("div",{className:"flex flex-col space-y-4 sm:flex-row sm:items-center sm:space-x-6 sm:space-y-0",children:[(0,n.jsx)("p",{className:"text-sm text-gray-400",children:"\xa9 2024 CashBoost. Todos os direitos reservados."}),(0,n.jsx)("div",{className:"flex space-x-4",children:Object.entries(M).map(([e,t])=>(0,n.jsx)("a",{href:m[e],className:"text-gray-400 transition-colors duration-200 hover:text-emerald-400",target:"_blank",rel:"noopener noreferrer","aria-label":`Seguir no ${e}`,children:t},e))})]}),(0,n.jsxs)(a.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"mt-6 inline-flex items-center space-x-2 text-gray-400 transition-colors duration-200 hover:text-emerald-400 lg:mt-0",children:[(0,n.jsx)("span",{className:"text-sm",children:"Voltar ao Topo"}),(0,n.jsx)(P.A,{className:"h-4 w-4"})]})]})})]})})}},4674:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return a}});let n=r(4949),i=r(1550),a=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:a}=(0,i.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+a};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4712:(e,t,r)=>{"use strict";r.d(t,{Footer:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/workspace/novo/src/components/layout/Footer.tsx","Footer")},4949:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},5076:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return a},prefetchReducer:function(){return s}});let n=r(5144),i=r(5334),a=new n.PromiseQueue(5),s=function(e,t){(0,i.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;return(0,i.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5144:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let n=r(1499),i=r(8919);var a=i._("_maxConcurrency"),s=i._("_runningCount"),o=i._("_queue"),l=i._("_processNext");class u{enqueue(e){let t,r,i=new Promise((e,n)=>{t=e,r=n}),a=async()=>{try{n._(this,s)[s]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,s)[s]--,n._(this,l)[l]()}};return n._(this,o)[o].push({promiseFn:i,task:a}),n._(this,l)[l](),i}bump(e){let t=n._(this,o)[o].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,o)[o].splice(t,1)[0];n._(this,o)[o].unshift(e),n._(this,l)[l](!0)}}constructor(e=5){Object.defineProperty(this,l,{value:c}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),Object.defineProperty(this,o,{writable:!0,value:void 0}),n._(this,a)[a]=e,n._(this,s)[s]=0,n._(this,o)[o]=[]}}function c(e){if(void 0===e&&(e=!1),(n._(this,s)[s]<n._(this,a)[a]||e)&&n._(this,o)[o].length>0){var t;null==(t=n._(this,o)[o].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5232:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return x},navigateReducer:function(){return function e(t,r){let{url:w,isExternalUrl:j,navigateType:P,shouldScroll:T,allowAliasing:E}=r,R={},{hash:M}=w,C=(0,i.createHrefFromUrl)(w),S="push"===P;if((0,g.prunePrefetchCache)(t.prefetchCache),R.preserveCustomHistoryState=!1,R.pendingPush=S,j)return x(t,R,w.toString(),S);if(document.getElementById("__next-page-redirect"))return x(t,R,C,S);let A=(0,g.getOrCreatePrefetchCacheEntry)({url:w,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:E}),{treeAtTimeOfPrefetch:N,data:k}=A;return h.prefetchQueue.bump(k),k.then(h=>{let{flightData:g,canonicalUrl:j,postponed:P}=h,E=Date.now(),k=!1;if(A.lastUsedTime||(A.lastUsedTime=E,k=!0),A.aliased){let n=(0,v.handleAliasedPrefetchEntry)(E,t,g,w,R);return!1===n?e(t,{...r,allowAliasing:!1}):n}if("string"==typeof g)return x(t,R,g,S);let _=j?(0,i.createHrefFromUrl)(j):C;if(M&&t.canonicalUrl.split("#",1)[0]===_.split("#",1)[0])return R.onlyHashChange=!0,R.canonicalUrl=_,R.shouldScroll=T,R.hashFragment=M,R.scrollableSegments=[],(0,c.handleMutable)(t,R);let O=t.tree,L=t.cache,D=[];for(let e of g){let{pathToSegment:r,seedData:i,head:c,isHeadPartial:h,isRootRender:g}=e,v=e.tree,j=["",...r],T=(0,s.applyRouterStatePatchToTree)(j,O,v,C);if(null===T&&(T=(0,s.applyRouterStatePatchToTree)(j,N,v,C)),null!==T){if(i&&g&&P){let e=(0,m.startPPRNavigation)(E,L,O,v,i,c,h,!1,D);if(null!==e){if(null===e.route)return x(t,R,C,S);T=e.route;let r=e.node;null!==r&&(R.cache=r);let i=e.dynamicRequestTree;if(null!==i){let r=(0,n.fetchServerResponse)(w,{flightRouterState:i,nextUrl:t.nextUrl});(0,m.listenForDynamicRequest)(e,r)}}else T=v}else{if((0,l.isNavigatingToNewRootLayout)(O,T))return x(t,R,C,S);let n=(0,f.createEmptyCacheNode)(),i=!1;for(let t of(A.status!==u.PrefetchCacheEntryStatus.stale||k?i=(0,d.applyFlightData)(E,L,n,e,A):(i=function(e,t,r,n){let i=!1;for(let a of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),b(n).map(e=>[...r,...e])))(0,y.clearCacheNodeDataForSegmentPath)(e,t,a),i=!0;return i}(n,L,r,v),A.lastUsedTime=E),(0,o.shouldHardNavigate)(j,O)?(n.rsc=L.rsc,n.prefetchRsc=L.prefetchRsc,(0,a.invalidateCacheBelowFlightSegmentPath)(n,L,r),R.cache=n):i&&(R.cache=n,L=n),b(v))){let e=[...r,...t];e[e.length-1]!==p.DEFAULT_SEGMENT_KEY&&D.push(e)}}O=T}}return R.patchedTree=O,R.canonicalUrl=_,R.scrollableSegments=D,R.hashFragment=M,R.shouldScroll=T,(0,c.handleMutable)(t,R)},()=>t)}}});let n=r(9008),i=r(7391),a=r(8468),s=r(6770),o=r(5951),l=r(2030),u=r(9154),c=r(9435),d=r(6928),h=r(5076),f=r(9752),p=r(3913),m=r(5956),g=r(5334),y=r(7464),v=r(9707);function x(e,t,r,n){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,c.handleMutable)(e,t)}function b(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,i]of Object.entries(n))for(let n of b(i))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}r(593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DYNAMIC_STALETIME_MS:function(){return h},STATIC_STALETIME_MS:function(){return f},createSeededPrefetchCacheEntry:function(){return u},getOrCreatePrefetchCacheEntry:function(){return l},prunePrefetchCache:function(){return d}});let n=r(9008),i=r(9154),a=r(5076);function s(e,t,r){let n=e.pathname;return(t&&(n+=e.search),r)?""+r+"%"+n:n}function o(e,t,r){return s(e,t===i.PrefetchKind.FULL,r)}function l(e){let{url:t,nextUrl:r,tree:n,prefetchCache:a,kind:o,allowAliasing:l=!0}=e,u=function(e,t,r,n,a){for(let o of(void 0===t&&(t=i.PrefetchKind.TEMPORARY),[r,null])){let r=s(e,!0,o),l=s(e,!1,o),u=e.search?r:l,c=n.get(u);if(c&&a){if(c.url.pathname===e.pathname&&c.url.search!==e.search)return{...c,aliased:!0};return c}let d=n.get(l);if(a&&e.search&&t!==i.PrefetchKind.FULL&&d&&!d.key.includes("%"))return{...d,aliased:!0}}if(t!==i.PrefetchKind.FULL&&a){for(let t of n.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,o,r,a,l);return u?(u.status=p(u),u.kind!==i.PrefetchKind.FULL&&o===i.PrefetchKind.FULL&&u.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return c({tree:n,url:t,nextUrl:r,prefetchCache:a,kind:null!=o?o:i.PrefetchKind.TEMPORARY})}),o&&u.kind===i.PrefetchKind.TEMPORARY&&(u.kind=o),u):c({tree:n,url:t,nextUrl:r,prefetchCache:a,kind:o||i.PrefetchKind.TEMPORARY})}function u(e){let{nextUrl:t,tree:r,prefetchCache:n,url:a,data:s,kind:l}=e,u=s.couldBeIntercepted?o(a,l,t):o(a,l),c={treeAtTimeOfPrefetch:r,data:Promise.resolve(s),kind:l,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:u,status:i.PrefetchCacheEntryStatus.fresh,url:a};return n.set(u,c),c}function c(e){let{url:t,kind:r,tree:s,nextUrl:l,prefetchCache:u}=e,c=o(t,r),d=a.prefetchQueue.enqueue(()=>(0,n.fetchServerResponse)(t,{flightRouterState:s,nextUrl:l,prefetchKind:r}).then(e=>{let r;if(e.couldBeIntercepted&&(r=function(e){let{url:t,nextUrl:r,prefetchCache:n,existingCacheKey:i}=e,a=n.get(i);if(!a)return;let s=o(t,a.kind,r);return n.set(s,{...a,key:s}),n.delete(i),s}({url:t,existingCacheKey:c,nextUrl:l,prefetchCache:u})),e.prerendered){let t=u.get(null!=r?r:c);t&&(t.kind=i.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),h={treeAtTimeOfPrefetch:s,data:d,kind:r,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:c,status:i.PrefetchCacheEntryStatus.fresh,url:t};return u.set(c,h),h}function d(e){for(let[t,r]of e)p(r)===i.PrefetchCacheEntryStatus.expired&&e.delete(t)}let h=1e3*Number("0"),f=1e3*Number("300");function p(e){let{kind:t,prefetchTime:r,lastUsedTime:n,staleTime:a}=e;return -1!==a?Date.now()<r+a?i.PrefetchCacheEntryStatus.fresh:i.PrefetchCacheEntryStatus.stale:Date.now()<(null!=n?n:r)+h?n?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.fresh:t===i.PrefetchKind.AUTO&&Date.now()<r+f?i.PrefetchCacheEntryStatus.stale:t===i.PrefetchKind.FULL&&Date.now()<r+f?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5416:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return n.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return a},getBotType:function(){return l},isBot:function(){return o}});let n=r(5796),i=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,a=n.HTML_LIMITED_BOT_UA_RE.source;function s(e){return n.HTML_LIMITED_BOT_UA_RE.test(e)}function o(e){return i.test(e)||s(e)}function l(e){return i.test(e)?"dom":s(e)?"html":void 0}},5796:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return r}});let r=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},5814:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},useLinkStatus:function(){return v}});let n=r(4441),i=r(687),a=n._(r(3210)),s=r(195),o=r(2142),l=r(9154),u=r(3038),c=r(9289),d=r(6127);r(148);let h=r(3406),f=r(1794),p=r(3690);function m(e){return"string"==typeof e?e:(0,s.formatUrl)(e)}function g(e){let t,r,n,[s,g]=(0,a.useOptimistic)(h.IDLE_LINK_STATUS),v=(0,a.useRef)(null),{href:x,as:b,children:w,prefetch:j=null,passHref:P,replace:T,shallow:E,scroll:R,onClick:M,onMouseEnter:C,onTouchStart:S,legacyBehavior:A=!1,onNavigate:N,ref:k,unstable_dynamicOnHover:_,...O}=e;t=w,A&&("string"==typeof t||"number"==typeof t)&&(t=(0,i.jsx)("a",{children:t}));let L=a.default.useContext(o.AppRouterContext),D=!1!==j,V=null===j?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:U,as:I}=a.default.useMemo(()=>{let e=m(x);return{href:e,as:b?m(b):e}},[x,b]);A&&(r=a.default.Children.only(t));let B=A?r&&"object"==typeof r&&r.ref:k,F=a.default.useCallback(e=>(null!==L&&(v.current=(0,h.mountLinkInstance)(e,U,L,V,D,g)),()=>{v.current&&((0,h.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,h.unmountPrefetchableInstance)(e)}),[D,U,L,V,g]),H={ref:(0,u.useMergedRef)(F,B),onClick(e){A||"function"!=typeof M||M(e),A&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),L&&(e.defaultPrevented||function(e,t,r,n,i,s,o){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(t)){i&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),a.default.startTransition(()=>{if(o){let e=!1;if(o({preventDefault:()=>{e=!0}}),e)return}(0,p.dispatchNavigateAction)(r||t,i?"replace":"push",null==s||s,n.current)})}}(e,U,I,v,T,R,N))},onMouseEnter(e){A||"function"!=typeof C||C(e),A&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),L&&D&&(0,h.onNavigationIntent)(e.currentTarget,!0===_)},onTouchStart:function(e){A||"function"!=typeof S||S(e),A&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),L&&D&&(0,h.onNavigationIntent)(e.currentTarget,!0===_)}};return(0,c.isAbsoluteUrl)(I)?H.href=I:A&&!P&&("a"!==r.type||"href"in r.props)||(H.href=(0,d.addBasePath)(I)),n=A?a.default.cloneElement(r,H):(0,i.jsx)("a",{...O,...H,children:t}),(0,i.jsx)(y.Provider,{value:s,children:n})}r(2708);let y=(0,a.createContext)(h.IDLE_LINK_STATUS),v=()=>(0,a.useContext)(y);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5942:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(6736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5951:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[a,s]=r,[o,l]=t;return(0,i.matchSegment)(o,a)?!(t.length<=2)&&e((0,n.getNextFlightSegmentPath)(t),s[l]):!!Array.isArray(o)}}});let n=r(4007),i=r(4077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5956:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{abortTask:function(){return p},listenForDynamicRequest:function(){return f},startPPRNavigation:function(){return u},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let n=r[1],i=t.parallelRoutes,s=new Map(i);for(let t in n){let r=n[t],o=r[0],l=(0,a.createRouterCacheKey)(o),u=i.get(t);if(void 0!==u){let n=u.get(l);if(void 0!==n){let i=e(n,r),a=new Map(u);a.set(l,i),s.set(t,a)}}}let o=t.rsc,l=y(o)&&"pending"===o.status;return{lazyData:null,rsc:o,head:t.head,prefetchHead:l?t.prefetchHead:[null,null],prefetchRsc:l?t.prefetchRsc:null,loading:t.loading,parallelRoutes:s,navigatedAt:t.navigatedAt}}}});let n=r(3913),i=r(4077),a=r(3123),s=r(2030),o=r(5334),l={route:null,node:null,dynamicRequestTree:null,children:null};function u(e,t,r,s,o,u,h,f,p){return function e(t,r,s,o,u,h,f,p,m,g,y){let v=s[1],x=o[1],b=null!==h?h[2]:null;u||!0===o[4]&&(u=!0);let w=r.parallelRoutes,j=new Map(w),P={},T=null,E=!1,R={};for(let r in x){let s,o=x[r],d=v[r],h=w.get(r),M=null!==b?b[r]:null,C=o[0],S=g.concat([r,C]),A=(0,a.createRouterCacheKey)(C),N=void 0!==d?d[0]:void 0,k=void 0!==h?h.get(A):void 0;if(null!==(s=C===n.DEFAULT_SEGMENT_KEY?void 0!==d?{route:d,node:null,dynamicRequestTree:null,children:null}:c(t,d,o,k,u,void 0!==M?M:null,f,p,S,y):m&&0===Object.keys(o[1]).length?c(t,d,o,k,u,void 0!==M?M:null,f,p,S,y):void 0!==d&&void 0!==N&&(0,i.matchSegment)(C,N)&&void 0!==k&&void 0!==d?e(t,k,d,o,u,M,f,p,m,S,y):c(t,d,o,k,u,void 0!==M?M:null,f,p,S,y))){if(null===s.route)return l;null===T&&(T=new Map),T.set(r,s);let e=s.node;if(null!==e){let t=new Map(h);t.set(A,e),j.set(r,t)}let t=s.route;P[r]=t;let n=s.dynamicRequestTree;null!==n?(E=!0,R[r]=n):R[r]=t}else P[r]=o,R[r]=o}if(null===T)return null;let M={lazyData:null,rsc:r.rsc,prefetchRsc:r.prefetchRsc,head:r.head,prefetchHead:r.prefetchHead,loading:r.loading,parallelRoutes:j,navigatedAt:t};return{route:d(o,P),node:M,dynamicRequestTree:E?d(o,R):null,children:T}}(e,t,r,s,!1,o,u,h,f,[],p)}function c(e,t,r,n,i,u,c,f,p,m){return!i&&(void 0===t||(0,s.isNavigatingToNewRootLayout)(t,r))?l:function e(t,r,n,i,s,l,u,c){let f,p,m,g,y=r[1],v=0===Object.keys(y).length;if(void 0!==n&&n.navigatedAt+o.DYNAMIC_STALETIME_MS>t)f=n.rsc,p=n.loading,m=n.head,g=n.navigatedAt;else if(null===i)return h(t,r,null,s,l,u,c);else if(f=i[1],p=i[3],m=v?s:null,g=t,i[4]||l&&v)return h(t,r,i,s,l,u,c);let x=null!==i?i[2]:null,b=new Map,w=void 0!==n?n.parallelRoutes:null,j=new Map(w),P={},T=!1;if(v)c.push(u);else for(let r in y){let n=y[r],i=null!==x?x[r]:null,o=null!==w?w.get(r):void 0,d=n[0],h=u.concat([r,d]),f=(0,a.createRouterCacheKey)(d),p=e(t,n,void 0!==o?o.get(f):void 0,i,s,l,h,c);b.set(r,p);let m=p.dynamicRequestTree;null!==m?(T=!0,P[r]=m):P[r]=n;let g=p.node;if(null!==g){let e=new Map;e.set(f,g),j.set(r,e)}}return{route:r,node:{lazyData:null,rsc:f,prefetchRsc:null,head:m,prefetchHead:null,loading:p,parallelRoutes:j,navigatedAt:g},dynamicRequestTree:T?d(r,P):null,children:b}}(e,r,n,u,c,f,p,m)}function d(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}function h(e,t,r,n,i,s,o){let l=d(t,t[1]);return l[3]="refetch",{route:t,node:function e(t,r,n,i,s,o,l){let u=r[1],c=null!==n?n[2]:null,d=new Map;for(let r in u){let n=u[r],h=null!==c?c[r]:null,f=n[0],p=o.concat([r,f]),m=(0,a.createRouterCacheKey)(f),g=e(t,n,void 0===h?null:h,i,s,p,l),y=new Map;y.set(m,g),d.set(r,y)}let h=0===d.size;h&&l.push(o);let f=null!==n?n[1]:null,p=null!==n?n[3]:null;return{lazyData:null,parallelRoutes:d,prefetchRsc:void 0!==f?f:null,prefetchHead:h?i:[null,null],loading:void 0!==p?p:null,rsc:v(),head:h?v():null,navigatedAt:t}}(e,t,r,n,i,s,o),dynamicRequestTree:l,children:null}}function f(e,t){t.then(t=>{let{flightData:r}=t;if("string"!=typeof r){for(let t of r){let{segmentPath:r,tree:n,seedData:s,head:o}=t;s&&function(e,t,r,n,s){let o=e;for(let e=0;e<t.length;e+=2){let r=t[e],n=t[e+1],a=o.children;if(null!==a){let e=a.get(r);if(void 0!==e){let t=e.route[0];if((0,i.matchSegment)(n,t)){o=e;continue}}}return}!function e(t,r,n,s){if(null===t.dynamicRequestTree)return;let o=t.children,l=t.node;if(null===o){null!==l&&(function e(t,r,n,s,o){let l=r[1],u=n[1],c=s[2],d=t.parallelRoutes;for(let t in l){let r=l[t],n=u[t],s=c[t],h=d.get(t),f=r[0],p=(0,a.createRouterCacheKey)(f),g=void 0!==h?h.get(p):void 0;void 0!==g&&(void 0!==n&&(0,i.matchSegment)(f,n[0])&&null!=s?e(g,r,n,s,o):m(r,g,null))}let h=t.rsc,f=s[1];null===h?t.rsc=f:y(h)&&h.resolve(f);let p=t.head;y(p)&&p.resolve(o)}(l,t.route,r,n,s),t.dynamicRequestTree=null);return}let u=r[1],c=n[2];for(let t in r){let r=u[t],n=c[t],a=o.get(t);if(void 0!==a){let t=a.route[0];if((0,i.matchSegment)(r[0],t)&&null!=n)return e(a,r,n,s)}}}(o,r,n,s)}(e,r,n,s,o)}p(e,null)}},t=>{p(e,t)})}function p(e,t){let r=e.node;if(null===r)return;let n=e.children;if(null===n)m(e.route,r,t);else for(let e of n.values())p(e,t);e.dynamicRequestTree=null}function m(e,t,r){let n=e[1],i=t.parallelRoutes;for(let e in n){let t=n[e],s=i.get(e);if(void 0===s)continue;let o=t[0],l=(0,a.createRouterCacheKey)(o),u=s.get(l);void 0!==u&&m(t,u,r)}let s=t.rsc;y(s)&&(null===r?s.resolve(null):s.reject(r));let o=t.head;y(o)&&o.resolve(null)}let g=Symbol();function y(e){return e&&e.tag===g}function v(){let e,t,r=new Promise((r,n)=>{e=r,t=n});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=g,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6001:(e,t,r)=>{"use strict";let n;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function a(e){let t=[{},{}];return e?.values.forEach((e,r)=>{t[0][r]=e.get(),t[1][r]=e.getVelocity()}),t}function s(e,t,r,n){if("function"==typeof t){let[i,s]=a(n);t=t(void 0!==r?r:e.custom,i,s)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,s]=a(n);t=t(void 0!==r?r:e.custom,i,s)}return t}function o(e,t,r){let n=e.getProps();return s(n,t,void 0!==r?r:n.custom,e)}function l(e,t){return e?.[t]??e?.default??e}r.d(t,{P:()=>aM});let u=e=>e,c={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],h={value:null,addProjectionMetrics:null};function f(e,t){let r=!1,n=!0,i={delta:0,timestamp:0,isProcessing:!1},a=()=>r=!0,s=d.reduce((e,r)=>(e[r]=function(e,t){let r=new Set,n=new Set,i=!1,a=!1,s=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){s.has(t)&&(c.schedule(t),e()),l++,t(o)}let c={schedule:(e,t=!1,a=!1)=>{let o=a&&i?r:n;return t&&s.add(e),o.has(e)||o.add(e),e},cancel:e=>{n.delete(e),s.delete(e)},process:e=>{if(o=e,i){a=!0;return}i=!0,[r,n]=[n,r],r.forEach(u),t&&h.value&&h.value.frameloop[t].push(l),l=0,r.clear(),i=!1,a&&(a=!1,c.process(e))}};return c}(a,t?r:void 0),e),{}),{setup:o,read:l,resolveKeyframes:u,preUpdate:f,update:p,preRender:m,render:g,postRender:y}=s,v=()=>{let a=c.useManualTiming?i.timestamp:performance.now();r=!1,c.useManualTiming||(i.delta=n?1e3/60:Math.max(Math.min(a-i.timestamp,40),1)),i.timestamp=a,i.isProcessing=!0,o.process(i),l.process(i),u.process(i),f.process(i),p.process(i),m.process(i),g.process(i),y.process(i),i.isProcessing=!1,r&&t&&(n=!1,e(v))},x=()=>{r=!0,n=!0,i.isProcessing||e(v)};return{schedule:d.reduce((e,t)=>{let n=s[t];return e[t]=(e,t=!1,i=!1)=>(r||x(),n.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<d.length;t++)s[d[t]].cancel(e)},state:i,steps:s}}let{schedule:p,cancel:m,state:g,steps:y}=f("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],x=new Set(v),b=new Set(["width","height","top","left","right","bottom",...v]);function w(e,t){-1===e.indexOf(t)&&e.push(t)}function j(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}class P{constructor(){this.subscriptions=[]}add(e){return w(this.subscriptions,e),()=>j(this.subscriptions,e)}notify(e,t,r){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](e,t,r);else for(let i=0;i<n;i++){let n=this.subscriptions[i];n&&n(e,t,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function T(){n=void 0}let E={now:()=>(void 0===n&&E.set(g.isProcessing||c.useManualTiming?g.timestamp:performance.now()),n),set:e=>{n=e,queueMicrotask(T)}},R=e=>!isNaN(parseFloat(e)),M={current:void 0};class C{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let r=E.now();if(this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=E.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=R(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new P);let r=this.events[e].add(t);return"change"===e?()=>{r(),p.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-r}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return M.current&&M.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=E.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let r=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),r?1e3/r*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function S(e,t){return new C(e,t)}let A=e=>Array.isArray(e),N=e=>!!(e&&e.getVelocity);function k(e,t){let r=e.getValue("willChange");if(N(r)&&r.add)return r.add(t);if(!r&&c.WillChange){let r=new c.WillChange("auto");e.addValue("willChange",r),r.add(t)}}let _=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),O="data-"+_("framerAppearId"),L=(e,t)=>r=>t(e(r)),D=(...e)=>e.reduce(L),V=(e,t,r)=>r>t?t:r<e?e:r,U=e=>1e3*e,I=e=>e/1e3,B={layout:0,mainThread:0,waapi:0},F=()=>{},H=()=>{},z=e=>t=>"string"==typeof t&&t.startsWith(e),W=z("--"),$=z("var(--"),K=e=>!!$(e)&&q.test(e.split("/*")[0].trim()),q=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,G={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},Y={...G,transform:e=>V(0,1,e)},X={...G,default:1},Z=e=>Math.round(1e5*e)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ee=(e,t)=>r=>!!("string"==typeof r&&J.test(r)&&r.startsWith(e)||t&&null!=r&&Object.prototype.hasOwnProperty.call(r,t)),et=(e,t,r)=>n=>{if("string"!=typeof n)return n;let[i,a,s,o]=n.match(Q);return{[e]:parseFloat(i),[t]:parseFloat(a),[r]:parseFloat(s),alpha:void 0!==o?parseFloat(o):1}},er=e=>V(0,255,e),en={...G,transform:e=>Math.round(er(e))},ei={test:ee("rgb","red"),parse:et("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:n=1})=>"rgba("+en.transform(e)+", "+en.transform(t)+", "+en.transform(r)+", "+Z(Y.transform(n))+")"},ea={test:ee("#"),parse:function(e){let t="",r="",n="",i="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),n=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),n=e.substring(3,4),i=e.substring(4,5),t+=t,r+=r,n+=n,i+=i),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}},transform:ei.transform},es=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),eo=es("deg"),el=es("%"),eu=es("px"),ec=es("vh"),ed=es("vw"),eh={...el,parse:e=>el.parse(e)/100,transform:e=>el.transform(100*e)},ef={test:ee("hsl","hue"),parse:et("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:n=1})=>"hsla("+Math.round(e)+", "+el.transform(Z(t))+", "+el.transform(Z(r))+", "+Z(Y.transform(n))+")"},ep={test:e=>ei.test(e)||ea.test(e)||ef.test(e),parse:e=>ei.test(e)?ei.parse(e):ef.test(e)?ef.parse(e):ea.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?ei.transform(e):ef.transform(e),getAnimatableNone:e=>{let t=ep.parse(e);return t.alpha=0,ep.transform(t)}},em=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eg="number",ey="color",ev=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ex(e){let t=e.toString(),r=[],n={color:[],number:[],var:[]},i=[],a=0,s=t.replace(ev,e=>(ep.test(e)?(n.color.push(a),i.push(ey),r.push(ep.parse(e))):e.startsWith("var(")?(n.var.push(a),i.push("var"),r.push(e)):(n.number.push(a),i.push(eg),r.push(parseFloat(e))),++a,"${}")).split("${}");return{values:r,split:s,indexes:n,types:i}}function eb(e){return ex(e).values}function ew(e){let{split:t,types:r}=ex(e),n=t.length;return e=>{let i="";for(let a=0;a<n;a++)if(i+=t[a],void 0!==e[a]){let t=r[a];t===eg?i+=Z(e[a]):t===ey?i+=ep.transform(e[a]):i+=e[a]}return i}}let ej=e=>"number"==typeof e?0:ep.test(e)?ep.getAnimatableNone(e):e,eP={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(Q)?.length||0)+(e.match(em)?.length||0)>0},parse:eb,createTransformer:ew,getAnimatableNone:function(e){let t=eb(e);return ew(e)(t.map(ej))}};function eT(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function eE(e,t){return r=>r>0?t:e}let eR=(e,t,r)=>e+(t-e)*r,eM=(e,t,r)=>{let n=e*e,i=r*(t*t-n)+n;return i<0?0:Math.sqrt(i)},eC=[ea,ei,ef],eS=e=>eC.find(t=>t.test(e));function eA(e){let t=eS(e);if(F(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let r=t.parse(e);return t===ef&&(r=function({hue:e,saturation:t,lightness:r,alpha:n}){e/=360,r/=100;let i=0,a=0,s=0;if(t/=100){let n=r<.5?r*(1+t):r+t-r*t,o=2*r-n;i=eT(o,n,e+1/3),a=eT(o,n,e),s=eT(o,n,e-1/3)}else i=a=s=r;return{red:Math.round(255*i),green:Math.round(255*a),blue:Math.round(255*s),alpha:n}}(r)),r}let eN=(e,t)=>{let r=eA(e),n=eA(t);if(!r||!n)return eE(e,t);let i={...r};return e=>(i.red=eM(r.red,n.red,e),i.green=eM(r.green,n.green,e),i.blue=eM(r.blue,n.blue,e),i.alpha=eR(r.alpha,n.alpha,e),ei.transform(i))},ek=new Set(["none","hidden"]);function e_(e,t){return r=>eR(e,t,r)}function eO(e){return"number"==typeof e?e_:"string"==typeof e?K(e)?eE:ep.test(e)?eN:eV:Array.isArray(e)?eL:"object"==typeof e?ep.test(e)?eN:eD:eE}function eL(e,t){let r=[...e],n=r.length,i=e.map((e,r)=>eO(e)(e,t[r]));return e=>{for(let t=0;t<n;t++)r[t]=i[t](e);return r}}function eD(e,t){let r={...e,...t},n={};for(let i in r)void 0!==e[i]&&void 0!==t[i]&&(n[i]=eO(e[i])(e[i],t[i]));return e=>{for(let t in n)r[t]=n[t](e);return r}}let eV=(e,t)=>{let r=eP.createTransformer(t),n=ex(e),i=ex(t);return n.indexes.var.length===i.indexes.var.length&&n.indexes.color.length===i.indexes.color.length&&n.indexes.number.length>=i.indexes.number.length?ek.has(e)&&!i.values.length||ek.has(t)&&!n.values.length?function(e,t){return ek.has(e)?r=>r<=0?e:t:r=>r>=1?t:e}(e,t):D(eL(function(e,t){let r=[],n={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let a=t.types[i],s=e.indexes[a][n[a]],o=e.values[s]??0;r[i]=o,n[a]++}return r}(n,i),i.values),r):(F(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eE(e,t))};function eU(e,t,r){return"number"==typeof e&&"number"==typeof t&&"number"==typeof r?eR(e,t,r):eO(e)(e,t)}let eI=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>p.update(t,e),stop:()=>m(t),now:()=>g.isProcessing?g.timestamp:E.now()}},eB=(e,t,r=10)=>{let n="",i=Math.max(Math.round(t/r),2);for(let t=0;t<i;t++)n+=Math.round(1e4*e(t/(i-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function eF(e){let t=0,r=e.next(t);for(;!r.done&&t<2e4;)t+=50,r=e.next(t);return t>=2e4?1/0:t}function eH(e,t,r){var n,i;let a=Math.max(t-5,0);return n=r-e(a),(i=t-a)?1e3/i*n:0}let ez={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eW(e,t){return e*Math.sqrt(1-t*t)}let e$=["duration","bounce"],eK=["stiffness","damping","mass"];function eq(e,t){return t.some(t=>void 0!==e[t])}function eG(e=ez.visualDuration,t=ez.bounce){let r,n="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:a}=n,s=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],l={done:!1,value:s},{stiffness:u,damping:c,mass:d,duration:h,velocity:f,isResolvedFromDuration:p}=function(e){let t={velocity:ez.velocity,stiffness:ez.stiffness,damping:ez.damping,mass:ez.mass,isResolvedFromDuration:!1,...e};if(!eq(e,eK)&&eq(e,e$))if(e.visualDuration){let r=2*Math.PI/(1.2*e.visualDuration),n=r*r,i=2*V(.05,1,1-(e.bounce||0))*Math.sqrt(n);t={...t,mass:ez.mass,stiffness:n,damping:i}}else{let r=function({duration:e=ez.duration,bounce:t=ez.bounce,velocity:r=ez.velocity,mass:n=ez.mass}){let i,a;F(e<=U(ez.maxDuration),"Spring duration must be 10 seconds or less");let s=1-t;s=V(ez.minDamping,ez.maxDamping,s),e=V(ez.minDuration,ez.maxDuration,I(e)),s<1?(i=t=>{let n=t*s,i=n*e;return .001-(n-r)/eW(t,s)*Math.exp(-i)},a=t=>{let n=t*s*e,a=Math.pow(s,2)*Math.pow(t,2)*e,o=Math.exp(-n),l=eW(Math.pow(t,2),s);return(n*r+r-a)*o*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-r)*e+1),a=t=>e*e*(r-t)*Math.exp(-t*e));let o=function(e,t,r){let n=r;for(let r=1;r<12;r++)n-=e(n)/t(n);return n}(i,a,5/e);if(e=U(e),isNaN(o))return{stiffness:ez.stiffness,damping:ez.damping,duration:e};{let t=Math.pow(o,2)*n;return{stiffness:t,damping:2*s*Math.sqrt(n*t),duration:e}}}(e);(t={...t,...r,mass:ez.mass}).isResolvedFromDuration=!0}return t}({...n,velocity:-I(n.velocity||0)}),m=f||0,g=c/(2*Math.sqrt(u*d)),y=o-s,v=I(Math.sqrt(u/d)),x=5>Math.abs(y);if(i||(i=x?ez.restSpeed.granular:ez.restSpeed.default),a||(a=x?ez.restDelta.granular:ez.restDelta.default),g<1){let e=eW(v,g);r=t=>o-Math.exp(-g*v*t)*((m+g*v*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===g)r=e=>o-Math.exp(-v*e)*(y+(m+v*y)*e);else{let e=v*Math.sqrt(g*g-1);r=t=>{let r=Math.exp(-g*v*t),n=Math.min(e*t,300);return o-r*((m+g*v*y)*Math.sinh(n)+e*y*Math.cosh(n))/e}}let b={calculatedDuration:p&&h||null,next:e=>{let t=r(e);if(p)l.done=e>=h;else{let n=0===e?m:0;g<1&&(n=0===e?U(m):eH(r,e,t));let s=Math.abs(o-t)<=a;l.done=Math.abs(n)<=i&&s}return l.value=l.done?o:t,l},toString:()=>{let e=Math.min(eF(b),2e4),t=eB(t=>b.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return b}function eY({keyframes:e,velocity:t=0,power:r=.8,timeConstant:n=325,bounceDamping:i=10,bounceStiffness:a=500,modifyTarget:s,min:o,max:l,restDelta:u=.5,restSpeed:c}){let d,h,f=e[0],p={done:!1,value:f},m=e=>void 0!==o&&e<o||void 0!==l&&e>l,g=e=>void 0===o?l:void 0===l||Math.abs(o-e)<Math.abs(l-e)?o:l,y=r*t,v=f+y,x=void 0===s?v:s(v);x!==v&&(y=x-f);let b=e=>-y*Math.exp(-e/n),w=e=>x+b(e),j=e=>{let t=b(e),r=w(e);p.done=Math.abs(t)<=u,p.value=p.done?x:r},P=e=>{m(p.value)&&(d=e,h=eG({keyframes:[p.value,g(p.value)],velocity:eH(w,e,p.value),damping:i,stiffness:a,restDelta:u,restSpeed:c}))};return P(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,j(e),P(e)),void 0!==d&&e>=d)?h.next(e-d):(t||j(e),p)}}}eG.applyToOptions=e=>{let t=function(e,t=100,r){let n=r({...e,keyframes:[0,t]}),i=Math.min(eF(n),2e4);return{type:"keyframes",ease:e=>n.next(i*e).value/t,duration:I(i)}}(e,100,eG);return e.ease=t.ease,e.duration=U(t.duration),e.type="keyframes",e};let eX=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function eZ(e,t,r,n){if(e===t&&r===n)return u;let i=t=>(function(e,t,r,n,i){let a,s,o=0;do(a=eX(s=t+(r-t)/2,n,i)-e)>0?r=s:t=s;while(Math.abs(a)>1e-7&&++o<12);return s})(t,0,1,e,r);return e=>0===e||1===e?e:eX(i(e),t,n)}let eQ=eZ(.42,0,1,1),eJ=eZ(0,0,.58,1),e0=eZ(.42,0,.58,1),e1=e=>Array.isArray(e)&&"number"!=typeof e[0],e2=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e5=e=>t=>1-e(1-t),e3=eZ(.33,1.53,.69,.99),e4=e5(e3),e6=e2(e4),e7=e=>(e*=2)<1?.5*e4(e):.5*(2-Math.pow(2,-10*(e-1))),e9=e=>1-Math.sin(Math.acos(e)),e8=e5(e9),te=e2(e9),tt=e=>Array.isArray(e)&&"number"==typeof e[0],tr={linear:u,easeIn:eQ,easeInOut:e0,easeOut:eJ,circIn:e9,circInOut:te,circOut:e8,backIn:e4,backInOut:e6,backOut:e3,anticipate:e7},tn=e=>"string"==typeof e,ti=e=>{if(tt(e)){H(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,r,n,i]=e;return eZ(t,r,n,i)}return tn(e)?(H(void 0!==tr[e],`Invalid easing type '${e}'`),tr[e]):e},ta=(e,t,r)=>{let n=t-e;return 0===n?1:(r-e)/n};function ts({duration:e=300,keyframes:t,times:r,ease:n="easeInOut"}){var i;let a=e1(n)?n.map(ti):ti(n),s={done:!1,value:t[0]},o=function(e,t,{clamp:r=!0,ease:n,mixer:i}={}){let a=e.length;if(H(a===t.length,"Both input and output ranges must be the same length"),1===a)return()=>t[0];if(2===a&&t[0]===t[1])return()=>t[1];let s=e[0]===e[1];e[0]>e[a-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,r){let n=[],i=r||c.mix||eU,a=e.length-1;for(let r=0;r<a;r++){let a=i(e[r],e[r+1]);t&&(a=D(Array.isArray(t)?t[r]||u:t,a)),n.push(a)}return n}(t,n,i),l=o.length,d=r=>{if(s&&r<e[0])return t[0];let n=0;if(l>1)for(;n<e.length-2&&!(r<e[n+1]);n++);let i=ta(e[n],e[n+1],r);return o[n](i)};return r?t=>d(V(e[0],e[a-1],t)):d}((i=r&&r.length===t.length?r:function(e){let t=[0];return!function(e,t){let r=e[e.length-1];for(let n=1;n<=t;n++){let i=ta(0,t,n);e.push(eR(r,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),t,{ease:Array.isArray(a)?a:t.map(()=>a||e0).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(s.value=o(t),s.done=t>=e,s)}}let to=e=>null!==e;function tl(e,{repeat:t,repeatType:r="loop"},n,i=1){let a=e.filter(to),s=i<0||t&&"loop"!==r&&t%2==1?0:a.length-1;return s&&void 0!==n?n:a[s]}let tu={decay:eY,inertia:eY,tween:ts,keyframes:ts,spring:eG};function tc(e){"string"==typeof e.type&&(e.type=tu[e.type])}class td{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let th=e=>e/100;class tf extends td{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==E.now()&&this.tick(E.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},B.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tc(e);let{type:t=ts,repeat:r=0,repeatDelay:n=0,repeatType:i,velocity:a=0}=e,{keyframes:s}=e,o=t||ts;o!==ts&&"number"!=typeof s[0]&&(this.mixKeyframes=D(th,eU(s[0],s[1])),s=[0,100]);let l=o({...e,keyframes:s});"mirror"===i&&(this.mirroredGenerator=o({...e,keyframes:[...s].reverse(),velocity:-a})),null===l.calculatedDuration&&(l.calculatedDuration=eF(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(r+1)-n,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:r,totalDuration:n,mixKeyframes:i,mirroredGenerator:a,resolvedDuration:s,calculatedDuration:o}=this;if(null===this.startTime)return r.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:h,type:f,onUpdate:p,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-n/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,x=r;if(c){let e=Math.min(this.currentTime,n)/s,t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,(t=Math.min(t,c+1))%2&&("reverse"===d?(r=1-r,h&&(r-=h/s)):"mirror"===d&&(x=a)),v=V(0,1,r)*s}let b=y?{done:!1,value:u[0]}:x.next(v);i&&(b.value=i(b.value));let{done:w}=b;y||null===o||(w=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let j=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return j&&f!==eY&&(b.value=tl(u,this.options,m,this.speed)),p&&p(b.value),j&&this.finish(),b}then(e,t){return this.finished.then(e,t)}get duration(){return I(this.calculatedDuration)}get time(){return I(this.currentTime)}set time(e){e=U(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(E.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=I(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eI,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let r=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=r):null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime||(this.startTime=t??r),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(E.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,B.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tp=e=>180*e/Math.PI,tm=e=>ty(tp(Math.atan2(e[1],e[0]))),tg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tm,rotateZ:tm,skewX:e=>tp(Math.atan(e[1])),skewY:e=>tp(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},ty=e=>((e%=360)<0&&(e+=360),e),tv=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tx=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tb={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tv,scaleY:tx,scale:e=>(tv(e)+tx(e))/2,rotateX:e=>ty(tp(Math.atan2(e[6],e[5]))),rotateY:e=>ty(tp(Math.atan2(-e[2],e[0]))),rotateZ:tm,rotate:tm,skewX:e=>tp(Math.atan(e[4])),skewY:e=>tp(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tw(e){return+!!e.includes("scale")}function tj(e,t){let r,n;if(!e||"none"===e)return tw(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)r=tb,n=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=tg,n=t}if(!n)return tw(t);let a=r[t],s=n[1].split(",").map(tT);return"function"==typeof a?a(s):s[a]}let tP=(e,t)=>{let{transform:r="none"}=getComputedStyle(e);return tj(r,t)};function tT(e){return parseFloat(e.trim())}let tE=e=>e===G||e===eu,tR=new Set(["x","y","z"]),tM=v.filter(e=>!tR.has(e)),tC={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tj(t,"x"),y:(e,{transform:t})=>tj(t,"y")};tC.translateX=tC.x,tC.translateY=tC.y;let tS=new Set,tA=!1,tN=!1,tk=!1;function t_(){if(tN){let e=Array.from(tS).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),r=new Map;t.forEach(e=>{let t=function(e){let t=[];return tM.forEach(r=>{let n=e.getValue(r);void 0!==n&&(t.push([r,n.get()]),n.set(+!!r.startsWith("scale")))}),t}(e);t.length&&(r.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=r.get(e);t&&t.forEach(([t,r])=>{e.getValue(t)?.set(r)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tN=!1,tA=!1,tS.forEach(e=>e.complete(tk)),tS.clear()}function tO(){tS.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tN=!0)})}class tL{constructor(e,t,r,n,i,a=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=r,this.motionValue=n,this.element=i,this.isAsync=a}scheduleResolve(){this.state="scheduled",this.isAsync?(tS.add(this),tA||(tA=!0,p.read(tO),p.resolveKeyframes(t_))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:r,motionValue:n}=this;if(null===e[0]){let i=n?.get(),a=e[e.length-1];if(void 0!==i)e[0]=i;else if(r&&t){let n=r.readValue(t,a);null!=n&&(e[0]=n)}void 0===e[0]&&(e[0]=a),n&&void 0===i&&n.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tS.delete(this)}cancel(){"scheduled"===this.state&&(tS.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tD=e=>e.startsWith("--");function tV(e){let t;return()=>(void 0===t&&(t=e()),t)}let tU=tV(()=>void 0!==window.ScrollTimeline),tI={},tB=function(e,t){let r=tV(e);return()=>tI[t]??r()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tF=([e,t,r,n])=>`cubic-bezier(${e}, ${t}, ${r}, ${n})`,tH={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tF([0,.65,.55,1]),circOut:tF([.55,0,1,.45]),backIn:tF([.31,.01,.66,-.59]),backOut:tF([.33,1.53,.69,.99])};function tz(e){return"function"==typeof e&&"applyToOptions"in e}class tW extends td{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:r,keyframes:n,pseudoElement:i,allowFlatten:a=!1,finalKeyframe:s,onComplete:o}=e;this.isPseudoElement=!!i,this.allowFlatten=a,this.options=e,H("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return tz(e)&&tB()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,r,{delay:n=0,duration:i=300,repeat:a=0,repeatType:s="loop",ease:o="easeOut",times:l}={},u){let c={[t]:r};l&&(c.offset=l);let d=function e(t,r){if(t)return"function"==typeof t?tB()?eB(t,r):"ease-out":tt(t)?tF(t):Array.isArray(t)?t.map(t=>e(t,r)||tH.easeOut):tH[t]}(o,i);Array.isArray(d)&&(c.easing=d),h.value&&B.waapi++;let f={delay:n,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:a+1,direction:"reverse"===s?"alternate":"normal"};u&&(f.pseudoElement=u);let p=e.animate(c,f);return h.value&&p.finished.finally(()=>{B.waapi--}),p}(t,r,n,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=tl(n,this.options,s,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,r){tD(t)?e.style.setProperty(t,r):e.style[t]=r}(t,r,e),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return I(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return I(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=U(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tU())?(this.animation.timeline=e,u):t(this)}}let t$={anticipate:e7,backInOut:e6,circInOut:te};class tK extends tW{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in t$&&(e.ease=t$[e.ease])}(e),tc(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:r,onComplete:n,element:i,...a}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let s=new tf({...a,autoplay:!1}),o=U(this.finishedTime??this.time);t.setWithVelocity(s.sample(o-10).value,s.sample(o).value,10),s.stop()}}let tq=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eP.test(e)||"0"===e)&&!e.startsWith("url("));var tG,tY,tX=r(8171);let tZ=new Set(["opacity","clipPath","filter","transform"]),tQ=tV(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tJ extends td{constructor({autoplay:e=!0,delay:t=0,type:r="keyframes",repeat:n=0,repeatDelay:i=0,repeatType:a="loop",keyframes:s,name:o,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=E.now();let d={autoplay:e,delay:t,type:r,repeat:n,repeatDelay:i,repeatType:a,name:o,motionValue:l,element:u,...c},h=u?.KeyframeResolver||tL;this.keyframeResolver=new h(s,(e,t,r)=>this.onKeyframesResolved(e,t,d,!r),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,r,n){this.keyframeResolver=void 0;let{name:i,type:a,velocity:s,delay:o,isHandoff:l,onUpdate:d}=r;this.resolvedAt=E.now(),!function(e,t,r,n){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let a=e[e.length-1],s=tq(i,t),o=tq(a,t);return F(s===o,`You are trying to animate ${t} from "${i}" to "${a}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${a} via the \`style\` property.`),!!s&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let r=0;r<e.length;r++)if(e[r]!==t)return!0}(e)||("spring"===r||tz(r))&&n)}(e,i,a,s)&&((c.instantAnimations||!o)&&d?.(tl(e,r,t)),e[0]=e[e.length-1],r.duration=0,r.repeat=0);let h={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...r,keyframes:e},f=!l&&function(e){let{motionValue:t,name:r,repeatDelay:n,repeatType:i,damping:a,type:s}=e;if(!(0,tX.s)(t?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=t.owner.getProps();return tQ()&&r&&tZ.has(r)&&("transform"!==r||!l)&&!o&&!n&&"mirror"!==i&&0!==a&&"inertia"!==s}(h)?new tK({...h,element:h.motionValue.owner.current}):new tf(h);f.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=f.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=f}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tk=!0,tO(),t_(),tk=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t0=e=>null!==e,t1={type:"spring",stiffness:500,damping:25,restSpeed:10},t2=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t5={type:"keyframes",duration:.8},t3={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t4=(e,{keyframes:t})=>t.length>2?t5:x.has(e)?e.startsWith("scale")?t2(t[1]):t1:t3,t6=(e,t,r,n={},i,a)=>s=>{let o=l(n,e)||{},u=o.delay||n.delay||0,{elapsed:d=0}=n;d-=U(u);let h={keyframes:Array.isArray(r)?r:[null,r],ease:"easeOut",velocity:t.getVelocity(),...o,delay:-d,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{s(),o.onComplete&&o.onComplete()},name:e,motionValue:t,element:a?void 0:i};!function({when:e,delay:t,delayChildren:r,staggerChildren:n,staggerDirection:i,repeat:a,repeatType:s,repeatDelay:o,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(o)&&Object.assign(h,t4(e,h)),h.duration&&(h.duration=U(h.duration)),h.repeatDelay&&(h.repeatDelay=U(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let f=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(f=!0)),(c.instantAnimations||c.skipAnimations)&&(f=!0,h.duration=0,h.delay=0),h.allowFlatten=!o.type&&!o.ease,f&&!a&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:r="loop"},n){let i=e.filter(t0),a=t&&"loop"!==r&&t%2==1?0:i.length-1;return i[a]}(h.keyframes,o);if(void 0!==e)return void p.update(()=>{h.onUpdate(e),h.onComplete()})}return o.isSync?new tf(h):new tJ(h)};function t7(e,t,{delay:r=0,transitionOverride:n,type:i}={}){let{transition:a=e.getDefaultTransition(),transitionEnd:s,...u}=t;n&&(a=n);let c=[],d=i&&e.animationState&&e.animationState.getState()[i];for(let t in u){let n=e.getValue(t,e.latestValues[t]??null),i=u[t];if(void 0===i||d&&function({protectedKeys:e,needsAnimating:t},r){let n=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,n}(d,t))continue;let s={delay:r,...l(a||{},t)},o=n.get();if(void 0!==o&&!n.isAnimating&&!Array.isArray(i)&&i===o&&!s.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let r=e.props[O];if(r){let e=window.MotionHandoffAnimation(r,t,p);null!==e&&(s.startTime=e,h=!0)}}k(e,t),n.start(t6(t,n,i,e.shouldReduceMotion&&b.has(t)?{type:!1}:s,e,h));let f=n.animation;f&&c.push(f)}return s&&Promise.all(c).then(()=>{p.update(()=>{s&&function(e,t){let{transitionEnd:r={},transition:n={},...i}=o(e,t)||{};for(let t in i={...i,...r}){var a;let r=A(a=i[t])?a[a.length-1]||0:a;e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,S(r))}}(e,s)})}),c}function t9(e,t,r={}){let n=o(e,t,"exit"===r.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=n||{};r.transitionOverride&&(i=r.transitionOverride);let a=n?()=>Promise.all(t7(e,n,r)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(n=0)=>{let{delayChildren:a=0,staggerChildren:s,staggerDirection:o}=i;return function(e,t,r=0,n=0,i=1,a){let s=[],o=(e.variantChildren.size-1)*n,l=1===i?(e=0)=>e*n:(e=0)=>o-e*n;return Array.from(e.variantChildren).sort(t8).forEach((e,n)=>{e.notify("AnimationStart",t),s.push(t9(e,t,{...a,delay:r+l(n)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(s)}(e,t,a+n,s,o,r)}:()=>Promise.resolve(),{when:l}=i;if(!l)return Promise.all([a(),s(r.delay)]);{let[e,t]="beforeChildren"===l?[a,s]:[s,a];return e().then(()=>t())}}function t8(e,t){return e.sortNodePosition(t)}function re(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let n=0;n<r;n++)if(t[n]!==e[n])return!1;return!0}function rt(e){return"string"==typeof e||Array.isArray(e)}let rr=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],rn=["initial",...rr],ri=rn.length,ra=[...rr].reverse(),rs=rr.length;function ro(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rl(){return{animate:ro(!0),whileInView:ro(),whileHover:ro(),whileTap:ro(),whileDrag:ro(),whileFocus:ro(),exit:ro()}}class ru{constructor(e){this.isMounted=!1,this.node=e}update(){}}class rc extends ru{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:r})=>(function(e,t,r={}){let n;if(e.notify("AnimationStart",t),Array.isArray(t))n=Promise.all(t.map(t=>t9(e,t,r)));else if("string"==typeof t)n=t9(e,t,r);else{let i="function"==typeof t?o(e,t,r.custom):t;n=Promise.all(t7(e,i,r))}return n.then(()=>{e.notify("AnimationComplete",t)})})(e,t,r))),r=rl(),n=!0,a=t=>(r,n)=>{let i=o(e,n,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...n}=i;r={...r,...n,...t}}return r};function s(s){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let r=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(r.initial=t.props.initial),r}let r={};for(let e=0;e<ri;e++){let n=rn[e],i=t.props[n];(rt(i)||!1===i)&&(r[n]=i)}return r}(e.parent)||{},c=[],d=new Set,h={},f=1/0;for(let t=0;t<rs;t++){var p,m;let o=ra[t],g=r[o],y=void 0!==l[o]?l[o]:u[o],v=rt(y),x=o===s?g.isActive:null;!1===x&&(f=t);let b=y===u[o]&&y!==l[o]&&v;if(b&&n&&e.manuallyAnimateOnMount&&(b=!1),g.protectedKeys={...h},!g.isActive&&null===x||!y&&!g.prevProp||i(y)||"boolean"==typeof y)continue;let w=(p=g.prevProp,"string"==typeof(m=y)?m!==p:!!Array.isArray(m)&&!re(m,p)),j=w||o===s&&g.isActive&&!b&&v||t>f&&v,P=!1,T=Array.isArray(y)?y:[y],E=T.reduce(a(o),{});!1===x&&(E={});let{prevResolvedValues:R={}}=g,M={...R,...E},C=t=>{j=!0,d.has(t)&&(P=!0,d.delete(t)),g.needsAnimating[t]=!0;let r=e.getValue(t);r&&(r.liveStyle=!1)};for(let e in M){let t=E[e],r=R[e];if(h.hasOwnProperty(e))continue;let n=!1;(A(t)&&A(r)?re(t,r):t===r)?void 0!==t&&d.has(e)?C(e):g.protectedKeys[e]=!0:null!=t?C(e):d.add(e)}g.prevProp=y,g.prevResolvedValues=E,g.isActive&&(h={...h,...E}),n&&e.blockInitialAnimation&&(j=!1);let S=!(b&&w)||P;j&&S&&c.push(...T.map(e=>({animation:e,options:{type:o}})))}if(d.size){let t={};if("boolean"!=typeof l.initial){let r=o(e,Array.isArray(l.initial)?l.initial[0]:l.initial);r&&r.transition&&(t.transition=r.transition)}d.forEach(r=>{let n=e.getBaseTarget(r),i=e.getValue(r);i&&(i.liveStyle=!0),t[r]=n??null}),c.push({animation:t})}let g=!!c.length;return n&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(g=!1),n=!1,g?t(c):Promise.resolve()}return{animateChanges:s,setActive:function(t,n){if(r[t].isActive===n)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,n)),r[t].isActive=n;let i=s(t);for(let e in r)r[e].protectedKeys={};return i},setAnimateFunction:function(r){t=r(e)},getState:()=>r,reset:()=>{r=rl(),n=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let rd=0;class rh extends ru{constructor(){super(...arguments),this.id=rd++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;let n=this.node.animationState.setActive("exit",!e);t&&!e&&n.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let rf={x:!1,y:!1};function rp(e,t,r,n={passive:!0}){return e.addEventListener(t,r,n),()=>e.removeEventListener(t,r)}let rm=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function rg(e){return{point:{x:e.pageX,y:e.pageY}}}let ry=e=>t=>rm(t)&&e(t,rg(t));function rv(e,t,r,n){return rp(e,t,ry(r),n)}function rx({top:e,left:t,right:r,bottom:n}){return{x:{min:t,max:r},y:{min:e,max:n}}}function rb(e){return e.max-e.min}function rw(e,t,r,n=.5){e.origin=n,e.originPoint=eR(t.min,t.max,e.origin),e.scale=rb(r)/rb(t),e.translate=eR(r.min,r.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function rj(e,t,r,n){rw(e.x,t.x,r.x,n?n.originX:void 0),rw(e.y,t.y,r.y,n?n.originY:void 0)}function rP(e,t,r){e.min=r.min+t.min,e.max=e.min+rb(t)}function rT(e,t,r){e.min=t.min-r.min,e.max=e.min+rb(t)}function rE(e,t,r){rT(e.x,t.x,r.x),rT(e.y,t.y,r.y)}let rR=()=>({translate:0,scale:1,origin:0,originPoint:0}),rM=()=>({x:rR(),y:rR()}),rC=()=>({min:0,max:0}),rS=()=>({x:rC(),y:rC()});function rA(e){return[e("x"),e("y")]}function rN(e){return void 0===e||1===e}function rk({scale:e,scaleX:t,scaleY:r}){return!rN(e)||!rN(t)||!rN(r)}function r_(e){return rk(e)||rO(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function rO(e){var t,r;return(t=e.x)&&"0%"!==t||(r=e.y)&&"0%"!==r}function rL(e,t,r,n,i){return void 0!==i&&(e=n+i*(e-n)),n+r*(e-n)+t}function rD(e,t=0,r=1,n,i){e.min=rL(e.min,t,r,n,i),e.max=rL(e.max,t,r,n,i)}function rV(e,{x:t,y:r}){rD(e.x,t.translate,t.scale,t.originPoint),rD(e.y,r.translate,r.scale,r.originPoint)}function rU(e,t){e.min=e.min+t,e.max=e.max+t}function rI(e,t,r,n,i=.5){let a=eR(e.min,e.max,i);rD(e,t,r,a,n)}function rB(e,t){rI(e.x,t.x,t.scaleX,t.scale,t.originX),rI(e.y,t.y,t.scaleY,t.scale,t.originY)}function rF(e,t){return rx(function(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}let rH=({current:e})=>e?e.ownerDocument.defaultView:null;function rz(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let rW=(e,t)=>Math.abs(e-t);class r${constructor(e,t,{transformPagePoint:r,contextWindow:n,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=rG(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){return Math.sqrt(rW(e.x,t.x)**2+rW(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!r)return;let{point:n}=e,{timestamp:i}=g;this.history.push({...n,timestamp:i});let{onStart:a,onMove:s}=this.handlers;t||(a&&a(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rK(t,this.transformPagePoint),p.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:r,onSessionEnd:n,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=rG("pointercancel"===e.type?this.lastMoveEventInfo:rK(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,a),n&&n(e,a)},!rm(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=r,this.contextWindow=n||window;let a=rK(rg(e),this.transformPagePoint),{point:s}=a,{timestamp:o}=g;this.history=[{...s,timestamp:o}];let{onSessionStart:l}=t;l&&l(e,rG(a,this.history)),this.removeListeners=D(rv(this.contextWindow,"pointermove",this.handlePointerMove),rv(this.contextWindow,"pointerup",this.handlePointerUp),rv(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function rK(e,t){return t?{point:t(e.point)}:e}function rq(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rG({point:e},t){return{point:e,delta:rq(e,rY(t)),offset:rq(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,n=null,i=rY(e);for(;r>=0&&(n=e[r],!(i.timestamp-n.timestamp>U(.1)));)r--;if(!n)return{x:0,y:0};let a=I(i.timestamp-n.timestamp);if(0===a)return{x:0,y:0};let s={x:(i.x-n.x)/a,y:(i.y-n.y)/a};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(t,.1)}}function rY(e){return e[e.length-1]}function rX(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function rZ(e,t){let r=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,n]=[n,r]),{min:r,max:n}}function rQ(e,t,r){return{min:rJ(e,t),max:rJ(e,r)}}function rJ(e,t){return"number"==typeof e?e:e[t]||0}let r0=new WeakMap;class r1{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=rS(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new r$(e,{onSessionStart:e=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(rg(e).point)},onStart:(e,t)=>{let{drag:r,dragPropagation:n,onDragStart:i}=this.getProps();if(r&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(rf[e])return null;else return rf[e]=!0,()=>{rf[e]=!1};return rf.x||rf.y?null:(rf.x=rf.y=!0,()=>{rf.x=rf.y=!1})}(r),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rA(e=>{let t=this.getAxisMotionValue(e).get()||0;if(el.test(t)){let{projection:r}=this.visualElement;if(r&&r.layout){let n=r.layout.layoutBox[e];n&&(t=rb(n)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&p.postRender(()=>i(e,t)),k(this.visualElement,"transform");let{animationState:a}=this.visualElement;a&&a.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:r,dragDirectionLock:n,onDirectionLock:i,onDrag:a}=this.getProps();if(!r&&!this.openDragLock)return;let{offset:s}=t;if(n&&null===this.currentDirection){this.currentDirection=function(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}(s),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),a&&a(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>rA(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:rH(this.visualElement)})}stop(e,t){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:n}=t;this.startAnimation(n);let{onDragEnd:i}=this.getProps();i&&p.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:n}=this.getProps();if(!r||!r2(e,n,this.currentDirection))return;let i=this.getAxisMotionValue(e),a=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(a=function(e,{min:t,max:r},n){return void 0!==t&&e<t?e=n?eR(t,e,n.min):Math.max(e,t):void 0!==r&&e>r&&(e=n?eR(r,e,n.max):Math.min(e,r)),e}(a,this.constraints[e],this.elastic[e])),i.set(a)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;e&&rz(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&r?this.constraints=function(e,{top:t,left:r,bottom:n,right:i}){return{x:rX(e.x,r,i),y:rX(e.y,t,n)}}(r.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:rQ(e,"left","right"),y:rQ(e,"top","bottom")}}(t),n!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&rA(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:r}=this.getProps();if(!t||!rz(t))return!1;let n=t.current;H(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let a=function(e,t,r){let n=rF(e,r),{scroll:i}=t;return i&&(rU(n.x,i.offset.x),rU(n.y,i.offset.y)),n}(n,i.root,this.visualElement.getTransformPagePoint()),s=(e=i.layout.layoutBox,{x:rZ(e.x,a.x),y:rZ(e.y,a.y)});if(r){let e=r(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=rx(e))}return s}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:n,dragTransition:i,dragSnapToOrigin:a,onDragTransitionEnd:s}=this.getProps(),o=this.constraints||{};return Promise.all(rA(s=>{if(!r2(s,t,this.currentDirection))return;let l=o&&o[s]||{};a&&(l={min:0,max:0});let u={type:"inertia",velocity:r?e[s]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return k(this.visualElement,e),r.start(t6(e,r,0,t,this.visualElement,!1))}stopAnimation(){rA(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){rA(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,r=this.visualElement.getProps();return r[t]||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){rA(t=>{let{drag:r}=this.getProps();if(!r2(t,r,this.currentDirection))return;let{projection:n}=this.visualElement,i=this.getAxisMotionValue(t);if(n&&n.layout){let{min:r,max:a}=n.layout.layoutBox[t];i.set(e[t]-eR(r,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!rz(t)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};rA(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let r=t.get();n[e]=function(e,t){let r=.5,n=rb(e),i=rb(t);return i>n?r=ta(t.min,t.max-n,e.min):n>i&&(r=ta(e.min,e.max-i,t.min)),V(0,1,r)}({min:r,max:r},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),rA(t=>{if(!r2(t,e,null))return;let r=this.getAxisMotionValue(t),{min:i,max:a}=this.constraints[t];r.set(eR(i,a,n[t]))})}addListeners(){if(!this.visualElement.current)return;r0.set(this.visualElement,this);let e=rv(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();rz(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,n=r.addEventListener("measure",t);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),p.read(t);let i=rp(window,"resize",()=>this.scalePositionWithinConstraints()),a=r.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(rA(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),n(),a&&a()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:i=!1,dragElastic:a=.35,dragMomentum:s=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:n,dragConstraints:i,dragElastic:a,dragMomentum:s}}}function r2(e,t,r){return(!0===t||t===e)&&(null===r||r===e)}class r5 extends ru{constructor(e){super(e),this.removeGroupControls=u,this.removeListeners=u,this.controls=new r1(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let r3=e=>(t,r)=>{e&&p.postRender(()=>e(t,r))};class r4 extends ru{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(e){this.session=new r$(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rH(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:r3(e),onStart:r3(t),onMove:r,onEnd:(e,t)=>{delete this.session,n&&p.postRender(()=>n(e,t))}}}mount(){this.removePointerDownListener=rv(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var r6=r(687);let{schedule:r7}=f(queueMicrotask,!1);var r9=r(3210),r8=r(6044),ne=r(2157);let nt=(0,r9.createContext)({}),nr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nn(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ni={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eu.test(e))return e;else e=parseFloat(e);let r=nn(e,t.target.x),n=nn(e,t.target.y);return`${r}% ${n}%`}},na={};class ns extends r9.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:n}=this.props,{projection:i}=e;for(let e in nl)na[e]=nl[e],W(e)&&(na[e].isCSSVariable=!0);i&&(t.group&&t.group.add(i),r&&r.register&&n&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),nr.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:n,isPresent:i}=this.props,{projection:a}=r;return a&&(a.isPresent=i,n||e.layoutDependency!==t||void 0===t||e.isPresent!==i?a.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?a.promote():a.relegate()||p.postRender(()=>{let e=a.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),r7.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function no(e){let[t,r]=(0,r8.xQ)(),n=(0,r9.useContext)(ne.L);return(0,r6.jsx)(ns,{...e,layoutGroup:n,switchLayoutGroup:(0,r9.useContext)(nt),isPresent:t,safeToRemove:r})}let nl={borderRadius:{...ni,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ni,borderTopRightRadius:ni,borderBottomLeftRadius:ni,borderBottomRightRadius:ni,boxShadow:{correct:(e,{treeScale:t,projectionDelta:r})=>{let n=eP.parse(e);if(n.length>5)return e;let i=eP.createTransformer(e),a=+("number"!=typeof n[0]),s=r.x.scale*t.x,o=r.y.scale*t.y;n[0+a]/=s,n[1+a]/=o;let l=eR(s,o,.5);return"number"==typeof n[2+a]&&(n[2+a]/=l),"number"==typeof n[3+a]&&(n[3+a]/=l),i(n)}}};var nu=r(4479);function nc(e){return(0,nu.G)(e)&&"ownerSVGElement"in e}let nd=(e,t)=>e.depth-t.depth;class nh{constructor(){this.children=[],this.isDirty=!1}add(e){w(this.children,e),this.isDirty=!0}remove(e){j(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(nd),this.isDirty=!1,this.children.forEach(e)}}function nf(e){return N(e)?e.get():e}let np=["TopLeft","TopRight","BottomLeft","BottomRight"],nm=np.length,ng=e=>"string"==typeof e?parseFloat(e):e,ny=e=>"number"==typeof e||eu.test(e);function nv(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let nx=nw(0,.5,e8),nb=nw(.5,.95,u);function nw(e,t,r){return n=>n<e?0:n>t?1:r(ta(e,t,n))}function nj(e,t){e.min=t.min,e.max=t.max}function nP(e,t){nj(e.x,t.x),nj(e.y,t.y)}function nT(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function nE(e,t,r,n,i){return e-=t,e=n+1/r*(e-n),void 0!==i&&(e=n+1/i*(e-n)),e}function nR(e,t,[r,n,i],a,s){!function(e,t=0,r=1,n=.5,i,a=e,s=e){if(el.test(t)&&(t=parseFloat(t),t=eR(s.min,s.max,t/100)-s.min),"number"!=typeof t)return;let o=eR(a.min,a.max,n);e===a&&(o-=t),e.min=nE(e.min,t,r,o,i),e.max=nE(e.max,t,r,o,i)}(e,t[r],t[n],t[i],t.scale,a,s)}let nM=["x","scaleX","originX"],nC=["y","scaleY","originY"];function nS(e,t,r,n){nR(e.x,t,nM,r?r.x:void 0,n?n.x:void 0),nR(e.y,t,nC,r?r.y:void 0,n?n.y:void 0)}function nA(e){return 0===e.translate&&1===e.scale}function nN(e){return nA(e.x)&&nA(e.y)}function nk(e,t){return e.min===t.min&&e.max===t.max}function n_(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function nO(e,t){return n_(e.x,t.x)&&n_(e.y,t.y)}function nL(e){return rb(e.x)/rb(e.y)}function nD(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class nV{constructor(){this.members=[]}add(e){w(this.members,e),e.scheduleRender()}remove(e){if(j(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,r=this.members.findIndex(t=>e===t);if(0===r)return!1;for(let e=r;e>=0;e--){let r=this.members[e];if(!1!==r.isPresent){t=r;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;!1===n&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nU={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nI=["","X","Y","Z"],nB={visibility:"hidden"},nF=0;function nH(e,t,r,n){let{latestValues:i}=t;i[e]&&(r[e]=i[e],t.setStaticValue(e,0),n&&(n[e]=0))}function nz({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:n,resetTransform:i}){return class{constructor(e={},r=t?.()){this.id=nF++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,h.value&&(nU.nodes=nU.calculatedTargetDeltas=nU.calculatedProjections=0),this.nodes.forEach(nK),this.nodes.forEach(nJ),this.nodes.forEach(n0),this.nodes.forEach(nq),h.addProjectionMetrics&&h.addProjectionMetrics(nU)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new nh)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new P),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=nc(t)&&!(nc(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:r,layout:n,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||r)&&(this.isLayoutDirty=!0),e){let r,n=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(e,t){let r=E.now(),n=({timestamp:i})=>{let a=i-r;a>=250&&(m(n),e(a-t))};return p.setup(n,!0),()=>m(n)}(n,250),nr.hasAnimatedSinceResize&&(nr.hasAnimatedSinceResize=!1,this.nodes.forEach(nQ))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&i&&(r||n)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:r,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let a=this.options.transition||i.getDefaultTransition()||n6,{onLayoutAnimationStart:s,onLayoutAnimationComplete:o}=i.getProps(),u=!this.targetLayout||!nO(this.targetLayout,n),c=!t&&r;if(this.options.layoutRoot||this.resumeFrom||c||t&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...l(a,"layout"),onPlay:s,onComplete:o};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,c)}else t||nQ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n1),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:r}=t.options;if(!r)return;let n=r.props[O];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:e,layoutId:r}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",p,!(e||r))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nY);return}this.isUpdating||this.nodes.forEach(nX),this.isUpdating=!1,this.nodes.forEach(nZ),this.nodes.forEach(nW),this.nodes.forEach(n$),this.clearAllSnapshots();let e=E.now();g.delta=V(0,1e3/60,e-g.timestamp),g.timestamp=e,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,r7.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nG),this.sharedNodes.forEach(n2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,p.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){p.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||rb(this.snapshot.measuredBox.x)||rb(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=rS(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=n(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!nN(this.projectionDelta),r=this.getTransformTemplate(),n=r?r(this.latestValues,""):void 0,a=n!==this.prevTransformTemplateValue;e&&this.instance&&(t||r_(this.latestValues)||a)&&(i(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let r=this.measurePageBox(),n=this.removeElementScroll(r);return e&&(n=this.removeTransform(n)),n8((t=n).x),n8(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return rS();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(it))){let{scroll:e}=this.root;e&&(rU(t.x,e.offset.x),rU(t.y,e.offset.y))}return t}removeElementScroll(e){let t=rS();if(nP(t,e),this.scroll?.wasRoot)return t;for(let r=0;r<this.path.length;r++){let n=this.path[r],{scroll:i,options:a}=n;n!==this.root&&i&&a.layoutScroll&&(i.wasRoot&&nP(t,e),rU(t.x,i.offset.x),rU(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let r=rS();nP(r,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];!t&&n.options.layoutScroll&&n.scroll&&n!==n.root&&rB(r,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r_(n.latestValues)&&rB(r,n.latestValues)}return r_(this.latestValues)&&rB(r,this.latestValues),r}removeTransform(e){let t=rS();nP(t,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];if(!r.instance||!r_(r.latestValues))continue;rk(r.latestValues)&&r.updateSnapshot();let n=rS();nP(n,r.measurePageBox()),nS(t,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,n)}return r_(this.latestValues)&&nS(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let r=!!this.resumingFrom||this!==t;if(!(e||r&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:i}=this.options;if(this.layout&&(n||i)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rS(),this.relativeTargetOrigin=rS(),rE(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nP(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=rS(),this.targetWithTransforms=rS()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var a,s,o;this.forceRelativeParentToResolveTarget(),a=this.target,s=this.relativeTarget,o=this.relativeParent.target,rP(a.x,s.x,o.x),rP(a.y,s.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nP(this.target,this.layout.layoutBox),rV(this.target,this.targetDelta)):nP(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rS(),this.relativeTargetOrigin=rS(),rE(this.relativeTargetOrigin,this.target,e.target),nP(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}h.value&&nU.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||rk(this.parent.latestValues)||rO(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,r=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(r=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===g.timestamp&&(r=!1),r)return;let{layout:n,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||i))return;nP(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,s=this.treeScale.y;!function(e,t,r,n=!1){let i,a,s=r.length;if(s){t.x=t.y=1;for(let o=0;o<s;o++){a=(i=r[o]).projectionDelta;let{visualElement:s}=i.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(n&&i.options.layoutScroll&&i.scroll&&i!==i.root&&rB(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),a&&(t.x*=a.x.scale,t.y*=a.y.scale,rV(e,a)),n&&r_(i.latestValues)&&rB(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=rS());let{target:o}=e;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nT(this.prevProjectionDelta.x,this.projectionDelta.x),nT(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rj(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===a&&this.treeScale.y===s&&nD(this.projectionDelta.x,this.prevProjectionDelta.x)&&nD(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),h.value&&nU.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=rM(),this.projectionDelta=rM(),this.projectionDeltaWithTransform=rM()}setAnimationOrigin(e,t=!1){let r,n=this.snapshot,i=n?n.latestValues:{},a={...this.latestValues},s=rM();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=rS(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(n4));this.animationProgress=0,this.mixTargetDelta=t=>{let n=t/1e3;if(n5(s.x,e.x,n),n5(s.y,e.y,n),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,f,p,m,g;rE(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=o,g=n,n3(f.x,p.x,m.x,g),n3(f.y,p.y,m.y,g),r&&(u=this.relativeTarget,h=r,nk(u.x,h.x)&&nk(u.y,h.y))&&(this.isProjectionDirty=!1),r||(r=rS()),nP(r,this.relativeTarget)}l&&(this.animationValues=a,function(e,t,r,n,i,a){i?(e.opacity=eR(0,r.opacity??1,nx(n)),e.opacityExit=eR(t.opacity??1,0,nb(n))):a&&(e.opacity=eR(t.opacity??1,r.opacity??1,n));for(let i=0;i<nm;i++){let a=`border${np[i]}Radius`,s=nv(t,a),o=nv(r,a);(void 0!==s||void 0!==o)&&(s||(s=0),o||(o=0),0===s||0===o||ny(s)===ny(o)?(e[a]=Math.max(eR(ng(s),ng(o),n),0),(el.test(o)||el.test(s))&&(e[a]+="%")):e[a]=o)}(t.rotate||r.rotate)&&(e.rotate=eR(t.rotate||0,r.rotate||0,n))}(a,i,this.latestValues,n,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=p.update(()=>{nr.hasAnimatedSinceResize=!0,B.layout++,this.motionValue||(this.motionValue=S(0)),this.currentAnimation=function(e,t,r){let n=N(e)?e:S(e);return n.start(t6("",n,t,r)),n.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{B.layout--},onComplete:()=>{B.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:r,layout:n,latestValues:i}=e;if(t&&r&&n){if(this!==e&&this.layout&&n&&ie(this.options.animationType,this.layout.layoutBox,n.layoutBox)){r=this.target||rS();let t=rb(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;let n=rb(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+n}nP(t,r),rB(t,i),rj(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new nV),this.sharedNodes.get(e).add(t);let r=t.options.initialPromotionConfig;t.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){let n=this.getStack();n&&n.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:r}=e;if((r.z||r.rotate||r.rotateX||r.rotateY||r.rotateZ||r.skewX||r.skewY)&&(t=!0),!t)return;let n={};r.z&&nH("z",e,n,this.animationValues);for(let t=0;t<nI.length;t++)nH(`rotate${nI[t]}`,e,n,this.animationValues),nH(`skew${nI[t]}`,e,n,this.animationValues);for(let t in e.render(),n)e.setStaticValue(t,n[t]),this.animationValues&&(this.animationValues[t]=n[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return nB;let t={visibility:""},r=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=nf(e?.pointerEvents)||"",t.transform=r?r(this.latestValues,""):"none",t;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=nf(e?.pointerEvents)||""),this.hasProjected&&!r_(this.latestValues)&&(t.transform=r?r({},""):"none",this.hasProjected=!1),t}let i=n.animationValues||n.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,r){let n="",i=e.x.translate/t.x,a=e.y.translate/t.y,s=r?.z||0;if((i||a||s)&&(n=`translate3d(${i}px, ${a}px, ${s}px) `),(1!==t.x||1!==t.y)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),r){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:a,skewX:s,skewY:o}=r;e&&(n=`perspective(${e}px) ${n}`),t&&(n+=`rotate(${t}deg) `),i&&(n+=`rotateX(${i}deg) `),a&&(n+=`rotateY(${a}deg) `),s&&(n+=`skewX(${s}deg) `),o&&(n+=`skewY(${o}deg) `)}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(n+=`scale(${o}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,i),r&&(t.transform=r(i,t.transform));let{x:a,y:s}=this.projectionDelta;for(let e in t.transformOrigin=`${100*a.origin}% ${100*s.origin}% 0`,n.animationValues?t.opacity=n===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:t.opacity=n===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,na){if(void 0===i[e])continue;let{correct:r,applyTo:a,isCSSVariable:s}=na[e],o="none"===t.transform?i[e]:r(i[e],n);if(a){let e=a.length;for(let r=0;r<e;r++)t[a[r]]=o}else s?this.options.visualElement.renderState.vars[e]=o:t[e]=o}return this.options.layoutId&&(t.pointerEvents=n===this?nf(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(nY),this.root.sharedNodes.clear()}}}function nW(e){e.updateLayout()}function n$(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:r,measuredBox:n}=e.layout,{animationType:i}=e.options,a=t.source!==e.layout.source;"size"===i?rA(e=>{let n=a?t.measuredBox[e]:t.layoutBox[e],i=rb(n);n.min=r[e].min,n.max=n.min+i}):ie(i,t.layoutBox,r)&&rA(n=>{let i=a?t.measuredBox[n]:t.layoutBox[n],s=rb(r[n]);i.max=i.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[n].max=e.relativeTarget[n].min+s)});let s=rM();rj(s,r,t.layoutBox);let o=rM();a?rj(o,e.applyTransform(n,!0),t.measuredBox):rj(o,r,t.layoutBox);let l=!nN(s),u=!1;if(!e.resumeFrom){let n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:i,layout:a}=n;if(i&&a){let s=rS();rE(s,t.layoutBox,i.layoutBox);let o=rS();rE(o,r,a.layoutBox),nO(s,o)||(u=!0),n.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=s,e.relativeParent=n)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:t,delta:o,layoutDelta:s,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function nK(e){h.value&&nU.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function nq(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function nG(e){e.clearSnapshot()}function nY(e){e.clearMeasurements()}function nX(e){e.isLayoutDirty=!1}function nZ(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function nQ(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function nJ(e){e.resolveTargetDelta()}function n0(e){e.calcProjection()}function n1(e){e.resetSkewAndRotation()}function n2(e){e.removeLeadSnapshot()}function n5(e,t,r){e.translate=eR(t.translate,0,r),e.scale=eR(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function n3(e,t,r,n){e.min=eR(t.min,r.min,n),e.max=eR(t.max,r.max,n)}function n4(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let n6={duration:.45,ease:[.4,0,.1,1]},n7=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),n9=n7("applewebkit/")&&!n7("chrome/")?Math.round:u;function n8(e){e.min=n9(e.min),e.max=n9(e.max)}function ie(e,t,r){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(nL(t)-nL(r)))}function it(e){return e!==e.root&&e.scroll?.wasRoot}let ir=nz({attachResizeListener:(e,t)=>rp(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ii={current:void 0},ia=nz({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ii.current){let e=new ir({});e.mount(window),e.setOptions({layoutScroll:!0}),ii.current=e}return ii.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function is(e,t){let r=function(e,t,r){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,r=(void 0)??t.querySelectorAll(e);return r?Array.from(r):[]}return Array.from(e)}(e),n=new AbortController;return[r,{passive:!0,...t,signal:n.signal},()=>n.abort()]}function io(e){return!("touch"===e.pointerType||rf.x||rf.y)}function il(e,t,r){let{props:n}=e;e.animationState&&n.whileHover&&e.animationState.setActive("whileHover","Start"===r);let i=n["onHover"+r];i&&p.postRender(()=>i(t,rg(t)))}class iu extends ru{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[n,i,a]=is(e,r),s=e=>{if(!io(e))return;let{target:r}=e,n=t(r,e);if("function"!=typeof n||!r)return;let a=e=>{io(e)&&(n(e),r.removeEventListener("pointerleave",a))};r.addEventListener("pointerleave",a,i)};return n.forEach(e=>{e.addEventListener("pointerenter",s,i)}),a}(e,(e,t)=>(il(this.node,t,"Start"),e=>il(this.node,e,"End"))))}unmount(){}}class ic extends ru{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=D(rp(this.node.current,"focus",()=>this.onFocus()),rp(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let id=(e,t)=>!!t&&(e===t||id(e,t.parentElement)),ih=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ip=new WeakSet;function im(e){return t=>{"Enter"===t.key&&e(t)}}function ig(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let iy=(e,t)=>{let r=e.currentTarget;if(!r)return;let n=im(()=>{if(ip.has(r))return;ig(r,"down");let e=im(()=>{ig(r,"up")});r.addEventListener("keyup",e,t),r.addEventListener("blur",()=>ig(r,"cancel"),t)});r.addEventListener("keydown",n,t),r.addEventListener("blur",()=>r.removeEventListener("keydown",n),t)};function iv(e){return rm(e)&&!(rf.x||rf.y)}function ix(e,t,r){let{props:n}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&n.whileTap&&e.animationState.setActive("whileTap","Start"===r);let i=n["onTap"+("End"===r?"":r)];i&&p.postRender(()=>i(t,rg(t)))}class ib extends ru{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[n,i,a]=is(e,r),s=e=>{let n=e.currentTarget;if(!iv(e))return;ip.add(n);let a=t(n,e),s=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),ip.has(n)&&ip.delete(n),iv(e)&&"function"==typeof a&&a(e,{success:t})},o=e=>{s(e,n===window||n===document||r.useGlobalTarget||id(n,e.target))},l=e=>{s(e,!1)};window.addEventListener("pointerup",o,i),window.addEventListener("pointercancel",l,i)};return n.forEach(e=>{((r.useGlobalTarget?window:e).addEventListener("pointerdown",s,i),(0,tX.s)(e))&&(e.addEventListener("focus",e=>iy(e,i)),ih.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),a}(e,(e,t)=>(ix(this.node,t,"Start"),(e,{success:t})=>ix(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let iw=new WeakMap,ij=new WeakMap,iP=e=>{let t=iw.get(e.target);t&&t(e)},iT=e=>{e.forEach(iP)},iE={some:0,all:1};class iR extends ru{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:n="some",once:i}=e,a={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof n?n:iE[n]};return function(e,t,r){let n=function({root:e,...t}){let r=e||document;ij.has(r)||ij.set(r,{});let n=ij.get(r),i=JSON.stringify(t);return n[i]||(n[i]=new IntersectionObserver(iT,{root:e,...t})),n[i]}(t);return iw.set(e,r),n.observe(e),()=>{iw.delete(e),n.unobserve(e)}}(this.node.current,a,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:r,onViewportLeave:n}=this.node.getProps(),a=t?r:n;a&&a(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t))&&this.startObserver()}unmount(){}}let iM=(0,r9.createContext)({strict:!1});var iC=r(2582);let iS=(0,r9.createContext)({});function iA(e){return i(e.animate)||rn.some(t=>rt(e[t]))}function iN(e){return!!(iA(e)||e.variants)}function ik(e){return Array.isArray(e)?e.join(" "):e}var i_=r(7044);let iO={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iL={};for(let e in iO)iL[e]={isEnabled:t=>iO[e].some(e=>!!t[e])};let iD=Symbol.for("motionComponentSymbol");var iV=r(1279),iU=r(2743);function iI(e,{layout:t,layoutId:r}){return x.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!na[e]||"opacity"===e)}let iB=(e,t)=>t&&"number"==typeof e?t.transform(e):e,iF={...G,transform:Math.round},iH={borderWidth:eu,borderTopWidth:eu,borderRightWidth:eu,borderBottomWidth:eu,borderLeftWidth:eu,borderRadius:eu,radius:eu,borderTopLeftRadius:eu,borderTopRightRadius:eu,borderBottomRightRadius:eu,borderBottomLeftRadius:eu,width:eu,maxWidth:eu,height:eu,maxHeight:eu,top:eu,right:eu,bottom:eu,left:eu,padding:eu,paddingTop:eu,paddingRight:eu,paddingBottom:eu,paddingLeft:eu,margin:eu,marginTop:eu,marginRight:eu,marginBottom:eu,marginLeft:eu,backgroundPositionX:eu,backgroundPositionY:eu,rotate:eo,rotateX:eo,rotateY:eo,rotateZ:eo,scale:X,scaleX:X,scaleY:X,scaleZ:X,skew:eo,skewX:eo,skewY:eo,distance:eu,translateX:eu,translateY:eu,translateZ:eu,x:eu,y:eu,z:eu,perspective:eu,transformPerspective:eu,opacity:Y,originX:eh,originY:eh,originZ:eu,zIndex:iF,fillOpacity:Y,strokeOpacity:Y,numOctaves:iF},iz={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iW=v.length;function i$(e,t,r){let{style:n,vars:i,transformOrigin:a}=e,s=!1,o=!1;for(let e in t){let r=t[e];if(x.has(e)){s=!0;continue}if(W(e)){i[e]=r;continue}{let t=iB(r,iH[e]);e.startsWith("origin")?(o=!0,a[e]=t):n[e]=t}}if(!t.transform&&(s||r?n.transform=function(e,t,r){let n="",i=!0;for(let a=0;a<iW;a++){let s=v[a],o=e[s];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!s.startsWith("scale"):0===parseFloat(o))||r){let e=iB(o,iH[s]);if(!l){i=!1;let t=iz[s]||s;n+=`${t}(${e}) `}r&&(t[s]=e)}}return n=n.trim(),r?n=r(t,i?"":n):i&&(n="none"),n}(t,e.transform,r):n.transform&&(n.transform="none")),o){let{originX:e="50%",originY:t="50%",originZ:r=0}=a;n.transformOrigin=`${e} ${t} ${r}`}}let iK=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iq(e,t,r){for(let n in t)N(t[n])||iI(n,r)||(e[n]=t[n])}let iG={offset:"stroke-dashoffset",array:"stroke-dasharray"},iY={offset:"strokeDashoffset",array:"strokeDasharray"};function iX(e,{attrX:t,attrY:r,attrScale:n,pathLength:i,pathSpacing:a=1,pathOffset:s=0,...o},l,u,c){if(i$(e,o,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:h}=e;d.transform&&(h.transform=d.transform,delete d.transform),(h.transform||d.transformOrigin)&&(h.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),h.transform&&(h.transformBox=c?.transformBox??"fill-box",delete d.transformBox),void 0!==t&&(d.x=t),void 0!==r&&(d.y=r),void 0!==n&&(d.scale=n),void 0!==i&&function(e,t,r=1,n=0,i=!0){e.pathLength=1;let a=i?iG:iY;e[a.offset]=eu.transform(-n);let s=eu.transform(t),o=eu.transform(r);e[a.array]=`${s} ${o}`}(d,i,a,s,!1)}let iZ=()=>({...iK(),attrs:{}}),iQ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),iJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function i0(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||iJ.has(e)}let i1=e=>!i0(e);try{!function(e){"function"==typeof e&&(i1=t=>t.startsWith("on")?!i0(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let i2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function i5(e){if("string"!=typeof e||e.includes("-"));else if(i2.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var i3=r(2789);let i4=e=>(t,r)=>{let n=(0,r9.useContext)(iS),a=(0,r9.useContext)(iV.t),o=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},r,n,a){return{latestValues:function(e,t,r,n){let a={},o=n(e,{});for(let e in o)a[e]=nf(o[e]);let{initial:l,animate:u}=e,c=iA(e),d=iN(e);t&&d&&!c&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===u&&(u=t.animate));let h=!!r&&!1===r.initial,f=(h=h||!1===l)?u:l;if(f&&"boolean"!=typeof f&&!i(f)){let t=Array.isArray(f)?f:[f];for(let r=0;r<t.length;r++){let n=s(e,t[r]);if(n){let{transitionEnd:e,transition:t,...r}=n;for(let e in r){let t=r[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(a[e]=t)}for(let t in e)a[t]=e[t]}}}return a}(r,n,a,e),renderState:t()}})(e,t,n,a);return r?o():(0,i3.M)(o)};function i6(e,t,r){let{style:n}=e,i={};for(let a in n)(N(n[a])||t.style&&N(t.style[a])||iI(a,e)||r?.getValue(a)?.liveStyle!==void 0)&&(i[a]=n[a]);return i}let i7={useVisualState:i4({scrapeMotionValuesFromProps:i6,createRenderState:iK})};function i9(e,t,r){let n=i6(e,t,r);for(let r in e)(N(e[r])||N(t[r]))&&(n[-1!==v.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]);return n}let i8={useVisualState:i4({scrapeMotionValuesFromProps:i9,createRenderState:iZ})},ae=e=>t=>t.test(e),at=[G,eu,el,eo,ed,ec,{test:e=>"auto"===e,parse:e=>e}],ar=e=>at.find(ae(e)),an=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),ai=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,aa=e=>/^0[^.\s]+$/u.test(e),as=new Set(["brightness","contrast","saturate","opacity"]);function ao(e){let[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[n]=r.match(Q)||[];if(!n)return e;let i=r.replace(n,""),a=+!!as.has(t);return n!==r&&(a*=100),t+"("+a+i+")"}let al=/\b([a-z-]*)\(.*?\)/gu,au={...eP,getAnimatableNone:e=>{let t=e.match(al);return t?t.map(ao).join(" "):e}},ac={...iH,color:ep,backgroundColor:ep,outlineColor:ep,fill:ep,stroke:ep,borderColor:ep,borderTopColor:ep,borderRightColor:ep,borderBottomColor:ep,borderLeftColor:ep,filter:au,WebkitFilter:au},ad=e=>ac[e];function ah(e,t){let r=ad(e);return r!==au&&(r=eP),r.getAnimatableNone?r.getAnimatableNone(t):void 0}let af=new Set(["auto","none","0"]);class ap extends tL{constructor(e,t,r,n,i){super(e,t,r,n,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:r}=this;if(!t||!t.current)return;super.readKeyframes();for(let r=0;r<e.length;r++){let n=e[r];if("string"==typeof n&&K(n=n.trim())){let i=function e(t,r,n=1){H(n<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,a]=function(e){let t=ai.exec(e);if(!t)return[,];let[,r,n,i]=t;return[`--${r??n}`,i]}(t);if(!i)return;let s=window.getComputedStyle(r).getPropertyValue(i);if(s){let e=s.trim();return an(e)?parseFloat(e):e}return K(a)?e(a,r,n+1):a}(n,t.current);void 0!==i&&(e[r]=i),r===e.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!b.has(r)||2!==e.length)return;let[n,i]=e,a=ar(n),s=ar(i);if(a!==s)if(tE(a)&&tE(s))for(let t=0;t<e.length;t++){let r=e[t];"string"==typeof r&&(e[t]=parseFloat(r))}else tC[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,r=[];for(let t=0;t<e.length;t++){var n;(null===e[t]||("number"==typeof(n=e[t])?0===n:null===n||"none"===n||"0"===n||aa(n)))&&r.push(t)}r.length&&function(e,t,r){let n,i=0;for(;i<e.length&&!n;){let t=e[i];"string"==typeof t&&!af.has(t)&&ex(t).values.length&&(n=e[i]),i++}if(n&&r)for(let i of t)e[i]=ah(r,n)}(e,r,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:r}=this;if(!e||!e.current)return;"height"===r&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tC[r](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let n=t[t.length-1];void 0!==n&&e.getValue(r,n).jump(n,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:r}=this;if(!e||!e.current)return;let n=e.getValue(t);n&&n.jump(this.measuredOrigin,!1);let i=r.length-1,a=r[i];r[i]=tC[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,r])=>{e.getValue(t).set(r)}),this.resolveNoneKeyframes()}}let am=[...at,ep,eP],ag=e=>am.find(ae(e)),ay={current:null},av={current:!1},ax=new WeakMap,ab=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class aw{scrapeMotionValuesFromProps(e,t,r){return{}}constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:n,blockInitialAnimation:i,visualState:a},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tL,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=E.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,p.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=a;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=s,this.blockInitialAnimation=!!i,this.isControllingVariants=iA(t),this.isVariantNode=iN(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==o[e]&&N(t)&&t.set(o[e],!1)}}mount(e){this.current=e,ax.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),av.current||function(){if(av.current=!0,i_.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>ay.current=e.matches;e.addListener(t),t()}else ay.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ay.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let r;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let n=x.has(e);n&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&p.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),a=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),a(),r&&r(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in iL){let t=iL[e];if(!t)continue;let{isEnabled:r,Feature:n}=t;if(!this.features[e]&&n&&r(this.props)&&(this.features[e]=new n(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):rS()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<ab.length;t++){let r=ab[t];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let n=e["on"+r];n&&(this.propEventSubscriptions[r]=this.on(r,n))}this.prevMotionValues=function(e,t,r){for(let n in t){let i=t[n],a=r[n];if(N(i))e.addValue(n,i);else if(N(a))e.addValue(n,S(i,{owner:e}));else if(a!==i)if(e.hasValue(n)){let t=e.getValue(n);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(n);e.addValue(n,S(void 0!==t?t:i,{owner:e}))}}for(let n in r)void 0===t[n]&&e.removeValue(n);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let r=this.values.get(e);t!==r&&(r&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=S(null===t?void 0:t,{owner:this}),this.addValue(e,r)),r}readValue(e,t){let r=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=r&&("string"==typeof r&&(an(r)||aa(r))?r=parseFloat(r):!ag(r)&&eP.test(t)&&(r=ah(e,t)),this.setBaseTarget(e,N(r)?r.get():r)),N(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let n=s(this.props,r,this.presenceContext?.custom);n&&(t=n[e])}if(r&&void 0!==t)return t;let n=this.getBaseTargetFromProps(this.props,e);return void 0===n||N(n)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:n}on(e,t){return this.events[e]||(this.events[e]=new P),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class aj extends aw{constructor(){super(...arguments),this.KeyframeResolver=ap}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;N(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function aP(e,{style:t,vars:r},n,i){for(let a in Object.assign(e.style,t,i&&i.getProjectionStyles(n)),r)e.style.setProperty(a,r[a])}class aT extends aj{constructor(){super(...arguments),this.type="html",this.renderInstance=aP}readValueFromInstance(e,t){if(x.has(t))return this.projection?.isProjecting?tw(t):tP(e,t);{let r=window.getComputedStyle(e),n=(W(t)?r.getPropertyValue(t):r[t])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return rF(e,t)}build(e,t,r){i$(e,t,r.transformTemplate)}scrapeMotionValuesFromProps(e,t,r){return i6(e,t,r)}}let aE=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class aR extends aj{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=rS}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(x.has(t)){let e=ad(t);return e&&e.default||0}return t=aE.has(t)?t:_(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,r){return i9(e,t,r)}build(e,t,r){iX(e,t,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(e,t,r,n){for(let r in aP(e,t,void 0,n),t.attrs)e.setAttribute(aE.has(r)?r:_(r),t.attrs[r])}mount(e){this.isSVGTag=iQ(e.tagName),super.mount(e)}}let aM=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(r,n)=>"create"===n?e:(t.has(n)||t.set(n,e(n)),t.get(n))})}((tG={animation:{Feature:rc},exit:{Feature:rh},inView:{Feature:iR},tap:{Feature:ib},focus:{Feature:ic},hover:{Feature:iu},pan:{Feature:r4},drag:{Feature:r5,ProjectionNode:ia,MeasureLayout:no},layout:{ProjectionNode:ia,MeasureLayout:no}},tY=(e,t)=>i5(e)?new aR(t):new aT(t,{allowProjection:e!==r9.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:n,Component:i}){function a(e,a){var s,o,l;let u,c={...(0,r9.useContext)(iC.Q),...e,layoutId:function({layoutId:e}){let t=(0,r9.useContext)(ne.L).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:d}=c,h=function(e){let{initial:t,animate:r}=function(e,t){if(iA(e)){let{initial:t,animate:r}=e;return{initial:!1===t||rt(t)?t:void 0,animate:rt(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,r9.useContext)(iS));return(0,r9.useMemo)(()=>({initial:t,animate:r}),[ik(t),ik(r)])}(e),f=n(e,d);if(!d&&i_.B){o=0,l=0,(0,r9.useContext)(iM).strict;let e=function(e){let{drag:t,layout:r}=iL;if(!t&&!r)return{};let n={...t,...r};return{MeasureLayout:t?.isEnabled(e)||r?.isEnabled(e)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(c);u=e.MeasureLayout,h.visualElement=function(e,t,r,n,i){let{visualElement:a}=(0,r9.useContext)(iS),s=(0,r9.useContext)(iM),o=(0,r9.useContext)(iV.t),l=(0,r9.useContext)(iC.Q).reducedMotion,u=(0,r9.useRef)(null);n=n||s.renderer,!u.current&&n&&(u.current=n(e,{visualState:t,parent:a,props:r,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let c=u.current,d=(0,r9.useContext)(nt);c&&!c.projection&&i&&("html"===c.type||"svg"===c.type)&&function(e,t,r,n){let{layoutId:i,layout:a,drag:s,dragConstraints:o,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new r(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:a,alwaysMeasureLayout:!!s||o&&rz(o),visualElement:e,animationType:"string"==typeof a?a:"both",initialPromotionConfig:n,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,r,i,d);let h=(0,r9.useRef)(!1);(0,r9.useInsertionEffect)(()=>{c&&h.current&&c.update(r,o)});let f=r[O],p=(0,r9.useRef)(!!f&&!window.MotionHandoffIsComplete?.(f)&&window.MotionHasOptimisedAnimation?.(f));return(0,iU.E)(()=>{c&&(h.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),r7.render(c.render),p.current&&c.animationState&&c.animationState.animateChanges())}),(0,r9.useEffect)(()=>{c&&(!p.current&&c.animationState&&c.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(f)}),p.current=!1))}),c}(i,f,c,t,e.ProjectionNode)}return(0,r6.jsxs)(iS.Provider,{value:h,children:[u&&h.visualElement?(0,r6.jsx)(u,{visualElement:h.visualElement,...c}):null,r(i,e,(s=h.visualElement,(0,r9.useCallback)(e=>{e&&f.onMount&&f.onMount(e),s&&(e?s.mount(e):s.unmount()),a&&("function"==typeof a?a(e):rz(a)&&(a.current=e))},[s])),f,d,h.visualElement)]})}e&&function(e){for(let t in e)iL[t]={...iL[t],...e[t]}}(e),a.displayName=`motion.${"string"==typeof i?i:`create(${i.displayName??i.name??""})`}`;let s=(0,r9.forwardRef)(a);return s[iD]=i,s}({...i5(e)?i8:i7,preloadedFeatures:tG,useRender:function(e=!1){return(t,r,n,{latestValues:i},a)=>{let s=(i5(t)?function(e,t,r,n){let i=(0,r9.useMemo)(()=>{let r=iZ();return iX(r,t,iQ(n),e.transformTemplate,e.style),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};iq(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let r={},n=function(e,t){let r=e.style||{},n={};return iq(n,r,e),Object.assign(n,function({transformTemplate:e},t){return(0,r9.useMemo)(()=>{let r=iK();return i$(r,t,e),Object.assign({},r.vars,r.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r})(r,i,a,t),o=function(e,t,r){let n={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(i1(i)||!0===r&&i0(i)||!t&&!i0(i)||e.draggable&&i.startsWith("onDrag"))&&(n[i]=e[i]);return n}(r,"string"==typeof t,e),l=t!==r9.Fragment?{...o,...s,ref:n}:{},{children:u}=r,c=(0,r9.useMemo)(()=>N(u)?u.get():u,[u]);return(0,r9.createElement)(t,{...l,children:c})}}(t),createVisualElement:tY,Component:e})}))},6022:(e,t,r)=>{Promise.resolve().then(r.bind(r,7588)),Promise.resolve().then(r.bind(r,8378)),Promise.resolve().then(r.bind(r,4650)),Promise.resolve().then(r.bind(r,9422)),Promise.resolve().then(r.bind(r,1194)),Promise.resolve().then(r.bind(r,9032))},6044:(e,t,r)=>{"use strict";r.d(t,{xQ:()=>a});var n=r(3210),i=r(1279);function a(e=!0){let t=(0,n.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:r,onExitComplete:s,register:o}=t,l=(0,n.useId)();(0,n.useEffect)(()=>{if(e)return o(l)},[e]);let u=(0,n.useCallback)(()=>e&&s&&s(l),[l,s,e]);return!r&&s?[!1,u]:[!0]}},6127:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return a}});let n=r(8834),i=r(4674);function a(e,t){return(0,i.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6361:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return i}});let n=r(6127);function i(e,t){if(e.startsWith(".")){let r=t.origin+t.pathname;return new URL((r.endsWith("/")?r:r+"/")+e)}return new URL((0,n.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6469:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>h,tree:()=>u});var n=r(5239),i=r(8088),a=r(8170),s=r.n(a),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let u=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"/home/<USER>/workspace/novo/src/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/home/<USER>/workspace/novo/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}],c=["/home/<USER>/workspace/novo/src/app/page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},6493:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return i}});let n=r(5232);function i(e,t,r){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6715:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(r,n(e));else t.set(r,n(i));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},6736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return i}});let n=r(2255);function i(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6770:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,n,l){let u,[c,d,h,f,p]=r;if(1===t.length){let e=o(r,n);return(0,s.addRefreshMarkerToActiveParallelSegments)(e,l),e}let[m,g]=t;if(!(0,a.matchSegment)(m,c))return null;if(2===t.length)u=o(d[g],n);else if(null===(u=e((0,i.getNextFlightSegmentPath)(t),d[g],n,l)))return null;let y=[t[0],{...d,[g]:u},h,f];return p&&(y[4]=!0),(0,s.addRefreshMarkerToActiveParallelSegments)(y,l),y}}});let n=r(3913),i=r(4007),a=r(4077),s=r(2308);function o(e,t){let[r,i]=e,[s,l]=t;if(s===n.DEFAULT_SEGMENT_KEY&&r!==n.DEFAULT_SEGMENT_KEY)return e;if((0,a.matchSegment)(r,s)){let t={};for(let e in i)void 0!==l[e]?t[e]=o(i[e],l[e]):t[e]=i[e];for(let e in l)t[e]||(t[e]=l[e]);let n=[r,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6928:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return a}});let n=r(1500),i=r(3898);function a(e,t,r,a,s){let{tree:o,seedData:l,head:u,isRootRender:c}=a;if(null===l)return!1;if(c){let i=l[1];r.loading=l[3],r.rsc=i,r.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(e,r,t,o,l,u,s)}else r.rsc=t.rsc,r.prefetchRsc=t.prefetchRsc,r.parallelRoutes=new Map(t.parallelRoutes),r.loading=t.loading,(0,i.fillCacheWithNewSubTreeData)(e,r,t,a,s);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7022:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return s}});let n=r(3210),i=r(1215),a="next-route-announcer";function s(e){let{tree:t}=e,[r,s]=(0,n.useState)(null);(0,n.useEffect)(()=>(s(function(){var e;let t=document.getElementsByName(a)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(a);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(a)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[o,l]=(0,n.useState)(""),u=(0,n.useRef)(void 0);return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&l(e),u.current=e},[t]),r?(0,i.createPortal)(o,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7044:(e,t,r)=>{"use strict";r.d(t,{B:()=>n});let n="undefined"!=typeof window},7464:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,r,a){let s=a.length<=2,[o,l]=a,u=(0,i.createRouterCacheKey)(l),c=r.parallelRoutes.get(o),d=t.parallelRoutes.get(o);d&&d!==c||(d=new Map(c),t.parallelRoutes.set(o,d));let h=null==c?void 0:c.get(u),f=d.get(u);if(s){f&&f.lazyData&&f!==h||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!f||!h){f||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return f===h&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),loading:f.loading},d.set(u,f)),e(f,h,(0,n.getNextFlightSegmentPath)(a))}}});let n=r(4007),i=r(3123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7549:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/workspace/novo/src/components/HeroSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/workspace/novo/src/components/HeroSection.tsx","default")},7588:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var n=r(687),i=r(3210),a=r(4364),s=r(942),o=r(922),l=r(6001);let u=[{id:1,store:"Amazon",logo:"/logos/amazon.svg",rate:"5.5%",platform:"Rakuten",position:{top:"15%",left:"10%"},delay:0,color:"#FF9900"},{id:2,store:"Nike",logo:"/logos/nike.svg",rate:"8.0%",platform:"Honey",position:{top:"25%",right:"15%"},delay:.5,color:"#000000"},{id:3,store:"Target",logo:"/logos/target.svg",rate:"3.5%",platform:"TopCashback",position:{bottom:"30%",left:"8%"},delay:1,color:"#CC0000"},{id:4,store:"Best Buy",logo:"/logos/bestbuy.svg",rate:"4.2%",platform:"Cashback Monitor",position:{bottom:"20%",right:"12%"},delay:1.5,color:"#0046BE"},{id:5,store:"Walmart",logo:"/logos/walmart.svg",rate:"2.8%",platform:"BeFrugal",position:{top:"45%",left:"5%"},delay:2,color:"#004C91"},{id:6,store:"Apple",logo:"/logos/apple.svg",rate:"1.5%",platform:"Rakuten",position:{top:"35%",right:"8%"},delay:2.5,color:"#000000"}];function c(){return(0,n.jsxs)("div",{className:"pointer-events-none absolute inset-0 overflow-hidden",children:[u.map(e=>(0,n.jsx)(l.P.div,{initial:{opacity:0,scale:.8,y:50},animate:{opacity:[0,1,1,.7],scale:[.8,1,1,.9],y:[50,0,-10,0]},transition:{duration:4,delay:e.delay,repeat:1/0,repeatType:"reverse",ease:"easeInOut"},className:"absolute hidden lg:block",style:e.position,children:(0,n.jsxs)("div",{className:"card-gradient shadow-green-soft min-w-[200px] rounded-2xl border border-white/20 p-4 backdrop-blur-sm",children:[(0,n.jsxs)("div",{className:"mb-3 flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"flex h-10 w-10 items-center justify-center rounded-xl shadow-sm",style:{backgroundColor:`${e.color}15`},children:(0,n.jsx)("div",{className:"flex h-6 w-6 items-center justify-center rounded text-xs font-bold text-white",style:{backgroundColor:e.color},children:e.store.charAt(0)})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-sm font-semibold text-gray-900",children:e.store}),(0,n.jsxs)("p",{className:"text-xs text-gray-500",children:["via ",e.platform]})]})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"mb-1 font-poppins text-2xl font-bold text-emerald-600",children:e.rate}),(0,n.jsx)("div",{className:"text-xs font-medium text-gray-500",children:"Cashback Rate"})]}),(0,n.jsx)("div",{className:"mt-2 flex items-center justify-center",children:(0,n.jsxs)(l.P.div,{animate:{scale:[1,1.1,1]},transition:{duration:2,repeat:1/0},className:"flex items-center space-x-1 text-xs text-emerald-600",children:[(0,n.jsx)("svg",{className:"h-3 w-3",fill:"currentColor",viewBox:"0 0 20 20",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z",clipRule:"evenodd"})}),(0,n.jsx)("span",{className:"font-medium",children:"Trending"})]})})]})},e.id)),(0,n.jsx)("div",{className:"lg:hidden",children:[1,2,3].map(e=>(0,n.jsx)(l.P.div,{initial:{opacity:0,scale:.5},animate:{opacity:[0,.6,.6,.3],scale:[.5,.8,.8,.6],y:[30,0,-5,0]},transition:{duration:3,delay:.5*e,repeat:1/0,repeatType:"reverse",ease:"easeInOut"},className:"absolute",style:{top:`${20+25*e}%`,[e%2==0?"left":"right"]:"5%"},children:(0,n.jsxs)("div",{className:"glass-green shadow-green-soft rounded-xl p-3",children:[(0,n.jsx)("div",{className:"text-lg font-bold text-emerald-600",children:1===e?"5.5%":2===e?"8.0%":"3.5%"}),(0,n.jsx)("div",{className:"text-xs text-emerald-700",children:"Cashback"})]})},`mobile-${e}`))}),(0,n.jsx)("div",{className:"absolute inset-0",children:[...Array(20)].map((e,t)=>(0,n.jsx)(l.P.div,{initial:{opacity:0,x:1200*Math.random(),y:800*Math.random()},animate:{opacity:[0,.3,0],x:1200*Math.random(),y:800*Math.random()},transition:{duration:10*Math.random()+10,repeat:1/0,repeatType:"reverse",ease:"linear",delay:5*Math.random()},className:"absolute h-1 w-1 rounded-full bg-white"},`particle-${t}`))}),[1,2,3,4,5].map(e=>(0,n.jsx)(l.P.div,{initial:{opacity:0,y:100,x:1200*Math.random()},animate:{opacity:[0,.4,0],y:-100},transition:{duration:8,repeat:1/0,delay:2*e,ease:"linear"},className:"pointer-events-none absolute text-2xl font-bold text-white/20",children:"$"},`dollar-${e}`))]})}let d=[{value:"2M+",label:"Usu\xe1rios Ativos",icon:a.A},{value:"500+",label:"Lojas Parceiras",icon:s.A},{value:"R$50M+",label:"Cashback Ganho",icon:a.A}];function h(){let[e,t]=(0,i.useState)("");return(0,n.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden pt-20",children:[(0,n.jsx)("div",{className:"absolute inset-0 hero-gradient"}),(0,n.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[(0,n.jsx)("div",{className:"absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-xl"}),(0,n.jsx)("div",{className:"absolute top-40 right-20 w-24 h-24 bg-white rounded-full blur-lg"}),(0,n.jsx)("div",{className:"absolute bottom-40 left-20 w-40 h-40 bg-white rounded-full blur-2xl"}),(0,n.jsx)("div",{className:"absolute bottom-20 right-10 w-28 h-28 bg-white rounded-full blur-xl"})]}),(0,n.jsx)(c,{}),(0,n.jsx)("div",{className:"relative z-10 container-responsive text-center",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,n.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"inline-flex items-center px-4 py-2 rounded-full glass-green text-emerald-700 font-medium text-sm mb-8",children:[(0,n.jsx)(a.A,{className:"w-4 h-4 mr-2"}),"Compare. Economize. Ganhe Mais."]}),(0,n.jsxs)(l.P.h1,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"text-responsive-3xl font-poppins font-black text-white mb-6 leading-tight",children:["Encontre as"," ",(0,n.jsxs)("span",{className:"relative",children:[(0,n.jsx)("span",{className:"bg-gradient-to-r from-yellow-300 via-yellow-200 to-yellow-100 bg-clip-text text-transparent",children:"Melhores Taxas de Cashback"}),(0,n.jsx)(l.P.div,{className:"absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-yellow-300 to-yellow-100 rounded-full",initial:{scaleX:0},animate:{scaleX:1},transition:{duration:.8,delay:1}})]})," ","em Todas as Plataformas"]}),(0,n.jsx)(l.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"text-responsive-lg text-emerald-50 mb-12 max-w-2xl mx-auto leading-relaxed",children:"Compare taxas de cashback ao vivo instantaneamente. Sem cadastro necess\xe1rio. Veja qual plataforma oferece as melhores taxas para suas lojas favoritas agora mesmo."}),(0,n.jsxs)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"max-w-2xl mx-auto mb-12",children:[(0,n.jsx)("form",{onSubmit:e=>{e.preventDefault()},className:"relative",children:(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(o.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-gray-400"}),(0,n.jsx)("input",{type:"text",value:e,onChange:e=>t(e.target.value),placeholder:"Busque por lojas como Amazon, Nike, Magazine Luiza...",className:"w-full pl-12 pr-40 py-4 text-lg rounded-2xl border-0 shadow-green-strong focus:ring-4 focus:ring-white/20 focus:shadow-green-strong bg-white/95 backdrop-blur-sm placeholder-gray-500 text-gray-900 outline-none transition-all duration-300"}),(0,n.jsx)("button",{type:"submit",className:"absolute right-3 top-1/2 transform -translate-y-1/2 bg-emerald-500 hover:bg-emerald-600 text-white font-semibold px-6 py-2.5 rounded-xl transition-all duration-200 shadow-md hover:shadow-lg",children:"Comparar Taxas"})]})}),(0,n.jsx)("div",{className:"mt-6 bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20",children:(0,n.jsxs)("div",{className:"flex flex-wrap justify-center items-center gap-3",children:[(0,n.jsx)("span",{className:"text-white font-medium text-sm",children:"Lojas populares:"}),["Amazon","Nike","Magazine Luiza","Americanas","Casas Bahia"].map(e=>(0,n.jsx)("button",{onClick:()=>t(e),className:"px-4 py-2 text-sm text-white bg-white/20 hover:bg-white/30 border border-white/30 hover:border-white/50 rounded-full transition-all duration-200 focus-visible font-medium",children:e},e))]})})]}),(0,n.jsx)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto",children:d.map((e,t)=>(0,n.jsxs)(l.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.6,delay:1+.1*t},className:"bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center",children:[(0,n.jsx)("div",{className:"inline-flex items-center justify-center w-12 h-12 rounded-xl bg-emerald-100 mb-4",children:(0,n.jsx)(e.icon,{className:"w-6 h-6 text-emerald-600"})}),(0,n.jsx)("div",{className:"text-2xl md:text-3xl font-poppins font-bold text-gray-900 mb-2",children:e.value}),(0,n.jsx)("div",{className:"text-gray-600 text-sm font-medium",children:e.label})]},e.label))}),(0,n.jsxs)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:1.2},className:"flex flex-col sm:flex-row gap-4 justify-center mt-12",children:[(0,n.jsx)("button",{className:"btn-primary text-lg px-8 py-4 animate-pulse-green",children:"Ver Taxas ao Vivo Abaixo ↓"}),(0,n.jsx)("button",{className:"btn-secondary bg-white/10 text-white border-white/20 hover:bg-white/20 text-lg px-8 py-4",children:"Comparar Todas as Plataformas"})]})]})}),(0,n.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:1,delay:1.5},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:(0,n.jsx)(l.P.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},className:"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center",children:(0,n.jsx)(l.P.div,{animate:{y:[0,12,0]},transition:{duration:2,repeat:1/0},className:"w-1 h-3 bg-white/60 rounded-full mt-2"})})})]})}},7681:()=>{},7748:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},7810:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return S}});let n=r(1264),i=r(1448),a=r(1563),s=r(9154),o=r(6361),l=r(7391),u=r(5232),c=r(6770),d=r(2030),h=r(9435),f=r(1500),p=r(9752),m=r(8214),g=r(6493),y=r(2308),v=r(4007),x=r(6875),b=r(7860),w=r(5334),j=r(5942),P=r(6736),T=r(4642);r(593);let{createFromFetch:E,createTemporaryReferenceSet:R,encodeReply:M}=r(9357);async function C(e,t,r){let s,l,{actionId:u,actionArgs:c}=r,d=R(),h=(0,T.extractInfoFromServerReferenceId)(u),f="use-cache"===h.type?(0,T.omitUnusedArgs)(c,h):c,p=await M(f,{temporaryReferences:d}),m=await fetch("",{method:"POST",headers:{Accept:a.RSC_CONTENT_TYPE_HEADER,[a.ACTION_HEADER]:u,[a.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...{},...t?{[a.NEXT_URL]:t}:{}},body:p}),g=m.headers.get("x-action-redirect"),[y,x]=(null==g?void 0:g.split(";"))||[];switch(x){case"push":s=b.RedirectType.push;break;case"replace":s=b.RedirectType.replace;break;default:s=void 0}let w=!!m.headers.get(a.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(m.headers.get("x-action-revalidated")||"[[],0,0]");l={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){l={paths:[],tag:!1,cookie:!1}}let j=y?(0,o.assignLocation)(y,new URL(e.canonicalUrl,window.location.href)):void 0,P=m.headers.get("content-type");if(null==P?void 0:P.startsWith(a.RSC_CONTENT_TYPE_HEADER)){let e=await E(Promise.resolve(m),{callServer:n.callServer,findSourceMapURL:i.findSourceMapURL,temporaryReferences:d});return y?{actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:j,redirectType:s,revalidatedParts:l,isPrerender:w}:{actionResult:e.a,actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:j,redirectType:s,revalidatedParts:l,isPrerender:w}}if(m.status>=400)throw Object.defineProperty(Error("text/plain"===P?await m.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:j,redirectType:s,revalidatedParts:l,isPrerender:w}}function S(e,t){let{resolve:r,reject:n}=t,i={},a=e.tree;i.preserveCustomHistoryState=!1;let o=e.nextUrl&&(0,m.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,v=Date.now();return C(e,o,t).then(async m=>{let T,{actionResult:E,actionFlightData:R,redirectLocation:M,redirectType:C,isPrerender:S,revalidatedParts:A}=m;if(M&&(C===b.RedirectType.replace?(e.pushRef.pendingPush=!1,i.pendingPush=!1):(e.pushRef.pendingPush=!0,i.pendingPush=!0),i.canonicalUrl=T=(0,l.createHrefFromUrl)(M,!1)),!R)return(r(E),M)?(0,u.handleExternalUrl)(e,i,M.href,e.pushRef.pendingPush):e;if("string"==typeof R)return r(E),(0,u.handleExternalUrl)(e,i,R,e.pushRef.pendingPush);let N=A.paths.length>0||A.tag||A.cookie;for(let n of R){let{tree:s,seedData:l,head:h,isRootRender:m}=n;if(!m)return r(E),e;let x=(0,c.applyRouterStatePatchToTree)([""],a,s,T||e.canonicalUrl);if(null===x)return r(E),(0,g.handleSegmentMismatch)(e,t,s);if((0,d.isNavigatingToNewRootLayout)(a,x))return r(E),(0,u.handleExternalUrl)(e,i,T||e.canonicalUrl,e.pushRef.pendingPush);if(null!==l){let t=l[1],r=(0,p.createEmptyCacheNode)();r.rsc=t,r.prefetchRsc=null,r.loading=l[3],(0,f.fillLazyItemsTillLeafWithHead)(v,r,void 0,s,l,h,void 0),i.cache=r,i.prefetchCache=new Map,N&&await (0,y.refreshInactiveParallelSegments)({navigatedAt:v,state:e,updatedTree:x,updatedCache:r,includeNextUrl:!!o,canonicalUrl:i.canonicalUrl||e.canonicalUrl})}i.patchedTree=x,a=x}return M&&T?(N||((0,w.createSeededPrefetchCacheEntry)({url:M,data:{flightData:R,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:S?s.PrefetchKind.FULL:s.PrefetchKind.AUTO}),i.prefetchCache=e.prefetchCache),n((0,x.getRedirectError)((0,P.hasBasePath)(T)?(0,j.removeBasePath)(T):T,C||b.RedirectType.push))):r(E),(0,h.handleMutable)(e,i)},t=>(n(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7936:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return n}}),r(9008),r(7391),r(6770),r(2030),r(5232),r(9435),r(6928),r(9752),r(6493),r(8214);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7953:()=>{},8171:(e,t,r)=>{"use strict";r.d(t,{s:()=>i});var n=r(4479);function i(e){return(0,n.G)(e)&&"offsetHeight"in e}},8378:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var n=r(687),i=r(922),a=r(3210);let s=a.forwardRef(function({title:e,titleId:t,...r},n){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))});var o=r(1082),l=r(4364),u=r(6001);let c=[{id:1,title:"Busque e Compare",description:"Digite o nome de qualquer loja ou navegue por categoria para ver taxas de cashback em tempo real de todas as principais plataformas.",icon:i.A,color:"from-blue-500 to-cyan-500",features:["500+ lojas","Taxas em tempo real","Todas as plataformas"]},{id:2,title:"Encontre as Melhores Taxas",description:"Nosso mecanismo de compara\xe7\xe3o inteligente mostra instantaneamente qual plataforma oferece o maior cashback para sua compra.",icon:s,color:"from-emerald-500 to-green-500",features:["Compara\xe7\xe3o inteligente","An\xe1lise de tend\xeancias","Alertas de taxa"]},{id:3,title:"Comece a Ganhar",description:"Clique na plataforma escolhida e comece a ganhar o m\xe1ximo de cashback em cada compra que fizer.",icon:o.A,color:"from-yellow-500 to-orange-500",features:["Ganhos m\xe1ximos","Rastreamento instant\xe2neo","Pagamentos f\xe1ceis"]}],d=["Economize tempo comparando taxas manualmente","Nunca perca as melhores ofertas de cashback","Maximize os ganhos em cada compra","Mantenha-se atualizado com mudan\xe7as de taxa","Acesse b\xf4nus exclusivos das plataformas","Acompanhe suas economias totais"];function h(){return(0,n.jsx)("section",{id:"how-it-works",className:"py-responsive bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50",children:(0,n.jsxs)("div",{className:"container-responsive",children:[(0,n.jsxs)("div",{className:"mb-16 text-center",children:[(0,n.jsxs)(u.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"mb-6 inline-flex items-center rounded-full bg-white/80 px-4 py-2 text-sm font-medium text-emerald-700 shadow-sm backdrop-blur-sm",children:[(0,n.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Processo Simples de 3 Passos"]}),(0,n.jsxs)(u.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1},viewport:{once:!0},className:"text-responsive-2xl mb-4 font-poppins font-bold text-gray-900",children:["Como o ",(0,n.jsx)("span",{className:"gradient-text",children:"CashBoost"})," Funciona"]}),(0,n.jsx)(u.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"text-responsive-sm mx-auto max-w-2xl text-gray-600",children:"Pare de perder tempo verificando v\xe1rios sites de cashback. Nossa plataforma faz o trabalho pesado, para que voc\xea possa focar no que mais importa - economizar dinheiro."})]}),(0,n.jsx)("div",{className:"mb-16 grid grid-cols-1 gap-8 lg:grid-cols-3 lg:gap-12",children:c.map((e,t)=>(0,n.jsxs)(u.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2*t},viewport:{once:!0},className:"relative",children:[t<c.length-1&&(0,n.jsx)("div",{className:"absolute left-full top-16 z-0 hidden h-0.5 w-12 bg-gradient-to-r from-emerald-200 to-emerald-300 lg:block"}),(0,n.jsxs)("div",{className:"relative z-10 text-center",children:[(0,n.jsx)(u.P.div,{whileHover:{scale:1.05,rotate:5},transition:{duration:.2},className:`inline-flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br ${e.color} group mb-6 cursor-pointer shadow-lg`,children:(0,n.jsx)(e.icon,{className:"h-10 w-10 text-white"})}),(0,n.jsx)("div",{className:"absolute -right-2 -top-2 flex h-8 w-8 items-center justify-center rounded-full bg-white shadow-md",children:(0,n.jsx)("span",{className:"text-sm font-bold text-emerald-600",children:e.id})}),(0,n.jsx)("h3",{className:"mb-4 font-poppins text-xl font-bold text-gray-900",children:e.title}),(0,n.jsx)("p",{className:"mb-6 leading-relaxed text-gray-600",children:e.description}),(0,n.jsx)("div",{className:"space-y-2",children:e.features.map((e,r)=>(0,n.jsxs)(u.P.div,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.4,delay:.2*t+.1*r},viewport:{once:!0},className:"mb-2 mr-2 inline-flex items-center rounded-full bg-white/60 px-3 py-1 text-sm font-medium text-emerald-700 backdrop-blur-sm",children:[(0,n.jsx)("div",{className:"mr-2 h-1.5 w-1.5 rounded-full bg-emerald-500"}),e]},e))})]})]},e.id))}),(0,n.jsxs)(u.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"rounded-3xl bg-white/80 p-8 shadow-xl backdrop-blur-sm lg:p-12",children:[(0,n.jsxs)("div",{className:"mb-8 text-center",children:[(0,n.jsx)("h3",{className:"mb-4 font-poppins text-2xl font-bold text-gray-900",children:"Por que o CashBoost se Destaca"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Junte-se a milhares de compradores inteligentes que j\xe1 est\xe3o maximizando seus ganhos de cashback"})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:d.map((e,t)=>(0,n.jsxs)(u.P.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},transition:{duration:.4,delay:.1*t},viewport:{once:!0},className:"flex items-center space-x-3 rounded-xl p-4 transition-colors duration-200 hover:bg-emerald-50/50",children:[(0,n.jsx)("div",{className:"flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-emerald-500",children:(0,n.jsx)("svg",{className:"h-3 w-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),(0,n.jsx)("span",{className:"font-medium text-gray-700",children:e})]},e))})]}),(0,n.jsxs)(u.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.6},viewport:{once:!0},className:"mt-12 text-center",children:[(0,n.jsx)("button",{className:"btn-primary animate-shine px-8 py-4 text-lg",children:"Comece a Comparar Taxas Agora"}),(0,n.jsx)("p",{className:"mt-4 text-sm text-gray-500",children:"Gratuito para usar • Sem cadastro necess\xe1rio • Resultados instant\xe2neos"})]})]})})}},8468:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,a){let s=a.length<=2,[o,l]=a,u=(0,n.createRouterCacheKey)(l),c=r.parallelRoutes.get(o);if(!c)return;let d=t.parallelRoutes.get(o);if(d&&d!==c||(d=new Map(c),t.parallelRoutes.set(o,d)),s)return void d.delete(u);let h=c.get(u),f=d.get(u);f&&h&&(f===h&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes)},d.set(u,f)),e(f,h,(0,i.getNextFlightSegmentPath)(a)))}}});let n=r(3123),i=r(4007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8627:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return a}});let n=r(7391),i=r(642);function a(e,t){var r;let{url:a,tree:s}=t,o=(0,n.createHrefFromUrl)(a),l=s||e.tree,u=e.cache;return{canonicalUrl:o,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:u,prefetchCache:e.prefetchCache,tree:l,nextUrl:null!=(r=(0,i.extractPathFromFlightRouterState)(l))?r:a.pathname}}r(5956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8830:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(9154),r(5232),r(9651),r(8627),r(8866),r(5076),r(7936),r(7810);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8834:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let n=r(1550);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:a}=(0,n.parsePath)(e);return""+t+r+i+a}},8866:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return p}});let n=r(9008),i=r(7391),a=r(6770),s=r(2030),o=r(5232),l=r(9435),u=r(1500),c=r(9752),d=r(6493),h=r(8214),f=r(2308);function p(e,t){let{origin:r}=t,p={},m=e.canonicalUrl,g=e.tree;p.preserveCustomHistoryState=!1;let y=(0,c.createEmptyCacheNode)(),v=(0,h.hasInterceptionRouteInCurrentTree)(e.tree);y.lazyData=(0,n.fetchServerResponse)(new URL(m,r),{flightRouterState:[g[0],g[1],g[2],"refetch"],nextUrl:v?e.nextUrl:null});let x=Date.now();return y.lazyData.then(async r=>{let{flightData:n,canonicalUrl:c}=r;if("string"==typeof n)return(0,o.handleExternalUrl)(e,p,n,e.pushRef.pendingPush);for(let r of(y.lazyData=null,n)){let{tree:n,seedData:l,head:h,isRootRender:b}=r;if(!b)return e;let w=(0,a.applyRouterStatePatchToTree)([""],g,n,e.canonicalUrl);if(null===w)return(0,d.handleSegmentMismatch)(e,t,n);if((0,s.isNavigatingToNewRootLayout)(g,w))return(0,o.handleExternalUrl)(e,p,m,e.pushRef.pendingPush);let j=c?(0,i.createHrefFromUrl)(c):void 0;if(c&&(p.canonicalUrl=j),null!==l){let e=l[1],t=l[3];y.rsc=e,y.prefetchRsc=null,y.loading=t,(0,u.fillLazyItemsTillLeafWithHead)(x,y,void 0,n,l,h,void 0),p.prefetchCache=new Map}await (0,f.refreshInactiveParallelSegments)({navigatedAt:x,state:e,updatedTree:w,updatedCache:y,includeNextUrl:v,canonicalUrl:p.canonicalUrl||e.canonicalUrl}),p.cache=y,p.patchedTree=w,g=w}return(0,l.handleMutable)(e,p)},()=>e)}r(593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8919:(e,t,r)=>{"use strict";r.r(t),r.d(t,{_:()=>i});var n=0;function i(e){return"__private_"+n+++"_"+e}},8926:(e,t,r)=>{"use strict";r.d(t,{Header:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/workspace/novo/src/components/layout/Header.tsx","Header")},8949:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/workspace/novo/src/components/TrustSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/workspace/novo/src/components/TrustSection.tsx","default")},9032:(e,t,r)=>{"use strict";r.d(t,{default:()=>m});var n=r(687),i=r(6001),a=r(3210);let s=a.forwardRef(function({title:e,titleId:t,...r},n){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))});var o=r(1082),l=r(942);let u=a.forwardRef(function({title:e,titleId:t,...r},n){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))}),c=a.forwardRef(function({title:e,titleId:t,...r},n){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z"}))}),d=a.forwardRef(function({title:e,titleId:t,...r},n){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 18.75h-9m9 0a3 3 0 0 1 3 3h-15a3 3 0 0 1 3-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 0 1-.982-3.172M9.497 14.25a7.454 7.454 0 0 0 .981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 0 0 7.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 0 0 2.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.492a46.32 46.32 0 0 1 2.916.52 6.003 6.003 0 0 1-5.395 4.972m0 0a6.726 6.726 0 0 1-2.749 1.35m0 0a6.772 6.772 0 0 1-3.044 0"}))}),h=[{id:1,value:"2.1M+",label:"Usu\xe1rios Satisfeitos",description:"Compradores confiam no CashBoost para suas necessidades de cashback",icon:s,color:"from-blue-500 to-cyan-500"},{id:2,value:"R$52M+",label:"Economia Total",description:"Cashback ganho pelos membros da nossa comunidade",icon:o.A,color:"from-emerald-500 to-green-500"},{id:3,value:"500+",label:"Lojas Parceiras",description:"Principais marcas e varejistas em nossa rede",icon:l.A,color:"from-purple-500 to-indigo-500"},{id:4,value:"4.9/5",label:"Avalia\xe7\xe3o dos Usu\xe1rios",description:"Avalia\xe7\xe3o m\xe9dia de usu\xe1rios verificados",icon:u,color:"from-yellow-500 to-orange-500"}],f=[{id:1,name:"Ana Silva",role:"Compradora Online Frequente",avatar:"/avatars/ana.jpg",rating:5,text:"O CashBoost mudou completamente como eu compro online. Economizei mais de R$800 este ano apenas comparando taxas antes de fazer compras. A interface \xe9 muito limpa e f\xe1cil de usar!",savings:"R$847",timeUsing:"8 meses"},{id:2,name:"Carlos Santos",role:"Entusiasta de Tecnologia",avatar:"/avatars/carlos.jpg",rating:5,text:"Como algu\xe9m que compra muitos eletr\xf4nicos, encontrar as melhores taxas de cashback sempre foi um problema. O CashBoost torna isso sem esfor\xe7o. Gostaria de ter encontrado esta plataforma antes!",savings:"R$1.240",timeUsing:"1 ano"},{id:3,name:"Mariana Costa",role:"Blogueira de Moda",avatar:"/avatars/mariana.jpg",rating:5,text:"As atualiza\xe7\xf5es de taxa em tempo real s\xe3o incr\xedveis. Peguei uma taxa de cashback de 12% na Nike que durou apenas algumas horas. Os alertas do CashBoost me salvaram de perder ofertas incr\xedveis.",savings:"R$623",timeUsing:"6 meses"}],p=[{title:"Atualiza\xe7\xf5es em Tempo Real",description:"Taxas de cashback atualizadas a cada hora",icon:"⚡"},{title:"Sem Taxas Ocultas",description:"Completamente gratuito para usar, sempre",icon:"\uD83D\uDCAF"},{title:"Taxas Verificadas",description:"Todas as taxas verificadas e precisas",icon:"✅"},{title:"Privacidade em Primeiro Lugar",description:"Seus dados permanecem privados e seguros",icon:"\uD83D\uDD12"}];function m(){let e=e=>Array.from({length:5},(t,r)=>(0,n.jsx)(u,{className:`w-4 h-4 ${r<e?"text-yellow-400 fill-current":"text-gray-300"}`},r));return(0,n.jsx)("section",{className:"py-responsive bg-white",children:(0,n.jsxs)("div",{className:"container-responsive",children:[(0,n.jsxs)("div",{className:"text-center mb-16",children:[(0,n.jsxs)(i.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"inline-flex items-center px-4 py-2 rounded-full bg-emerald-50 text-emerald-700 font-medium text-sm mb-6",children:[(0,n.jsx)(c,{className:"w-4 h-4 mr-2"}),"Confiado por Milh\xf5es"]}),(0,n.jsxs)(i.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1},viewport:{once:!0},className:"text-responsive-2xl font-poppins font-bold text-gray-900 mb-4",children:["Junte-se \xe0 Comunidade de"," ",(0,n.jsx)("span",{className:"gradient-text",children:"Compradores Inteligentes"})]}),(0,n.jsx)(i.P.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"text-responsive-sm text-gray-600 max-w-2xl mx-auto",children:"Milhares de compradores j\xe1 descobriram o poder da compara\xe7\xe3o inteligente de cashback. Veja o que eles est\xe3o dizendo sobre o CashBoost."})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16",children:h.map((e,t)=>(0,n.jsxs)(i.P.div,{initial:{opacity:0,y:30,scale:.9},whileInView:{opacity:1,y:0,scale:1},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"text-center group",children:[(0,n.jsx)(i.P.div,{whileHover:{scale:1.05,rotate:5},transition:{duration:.2},className:`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br ${e.color} shadow-lg mb-4 group-hover:shadow-xl transition-shadow duration-300`,children:(0,n.jsx)(e.icon,{className:"w-8 h-8 text-white"})}),(0,n.jsx)(i.P.div,{initial:{scale:.8},whileInView:{scale:1},transition:{duration:.8,delay:.1*t+.3},viewport:{once:!0},className:"text-3xl lg:text-4xl font-poppins font-black text-gray-900 mb-2",children:e.value}),(0,n.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:e.label}),(0,n.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:e.description})]},e.id))}),(0,n.jsxs)("div",{className:"mb-16",children:[(0,n.jsxs)(i.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-12",children:[(0,n.jsx)("h3",{className:"text-2xl font-poppins font-bold text-gray-900 mb-4",children:"O que Nossos Usu\xe1rios Dizem"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Hist\xf3rias reais de usu\xe1rios reais que est\xe3o economizando mais com o CashBoost"})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:f.map((t,r)=>(0,n.jsxs)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2*r},viewport:{once:!0},className:"bg-gradient-to-br from-gray-50 to-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4 mb-6",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full flex items-center justify-center shadow-md",children:(0,n.jsx)("span",{className:"text-white font-bold text-lg",children:t.name.charAt(0)})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-bold text-gray-900",children:t.name}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:t.role})]})]}),(0,n.jsx)("div",{className:"flex items-center space-x-1 mb-4",children:e(t.rating)}),(0,n.jsxs)("blockquote",{className:"text-gray-700 leading-relaxed mb-6 italic",children:['"',t.text,'"']}),(0,n.jsxs)("div",{className:"flex justify-between items-center pt-4 border-t border-gray-200",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-lg font-bold text-emerald-600",children:t.savings}),(0,n.jsx)("div",{className:"text-xs text-gray-500",children:"Total Economizado"})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-lg font-bold text-gray-900",children:t.timeUsing}),(0,n.jsx)("div",{className:"text-xs text-gray-500",children:"Usando CashBoost"})]})]})]},t.id))})]}),(0,n.jsxs)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"bg-gradient-to-br from-emerald-50 to-green-50 rounded-3xl p-8 lg:p-12",children:[(0,n.jsxs)("div",{className:"text-center mb-8",children:[(0,n.jsx)(d,{className:"w-12 h-12 text-emerald-600 mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-2xl font-poppins font-bold text-gray-900 mb-4",children:"Por que o CashBoost se Destaca"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Estamos comprometidos em fornecer a melhor experi\xeancia de compara\xe7\xe3o de cashback"})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:p.map((e,t)=>(0,n.jsxs)(i.P.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},transition:{duration:.4,delay:.1*t},viewport:{once:!0},className:"text-center p-6 bg-white/60 backdrop-blur-sm rounded-2xl hover:bg-white/80 transition-all duration-200",children:[(0,n.jsx)("div",{className:"text-3xl mb-3",children:e.icon}),(0,n.jsx)("h4",{className:"font-bold text-gray-900 mb-2",children:e.title}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]},e.title))})]})]})})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9289:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return f},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return o},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return x}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function s(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function o(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let h="undefined"!=typeof performance,f=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(e){return JSON.stringify({message:e.message,stack:e.stack})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9422:(e,t,r)=>{"use strict";r.d(t,{Header:()=>R});var n=r(687),i=r(922),a=r(3210);let s=a.forwardRef(function({title:e,titleId:t,...r},n){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}),o=a.forwardRef(function({title:e,titleId:t,...r},n){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))});var l=r(2157),u=r(2789),c=r(2743),d=r(1279),h=r(8171),f=r(2582);class p extends a.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,h.s)(e)&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function m({children:e,isPresent:t,anchorX:r}){let i=(0,a.useId)(),s=(0,a.useRef)(null),o=(0,a.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,a.useContext)(f.Q);return(0,a.useInsertionEffect)(()=>{let{width:e,height:n,top:a,left:u,right:c}=o.current;if(t||!s.current||!e||!n)return;let d="left"===r?`left: ${u}`:`right: ${c}`;s.current.dataset.motionPopId=i;let h=document.createElement("style");return l&&(h.nonce=l),document.head.appendChild(h),h.sheet&&h.sheet.insertRule(`
          [data-motion-pop-id="${i}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${n}px !important;
            ${d}px !important;
            top: ${a}px !important;
          }
        `),()=>{document.head.contains(h)&&document.head.removeChild(h)}},[t]),(0,n.jsx)(p,{isPresent:t,childRef:s,sizeRef:o,children:a.cloneElement(e,{ref:s})})}let g=({children:e,initial:t,isPresent:r,onExitComplete:i,custom:s,presenceAffectsLayout:o,mode:l,anchorX:c})=>{let h=(0,u.M)(y),f=(0,a.useId)(),p=!0,g=(0,a.useMemo)(()=>(p=!1,{id:f,initial:t,isPresent:r,custom:s,onExitComplete:e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;i&&i()},register:e=>(h.set(e,!1),()=>h.delete(e))}),[r,h,i]);return o&&p&&(g={...g}),(0,a.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[r]),a.useEffect(()=>{r||h.size||!i||i()},[r]),"popLayout"===l&&(e=(0,n.jsx)(m,{isPresent:r,anchorX:c,children:e})),(0,n.jsx)(d.t.Provider,{value:g,children:e})};function y(){return new Map}var v=r(6044);let x=e=>e.key||"";function b(e){let t=[];return a.Children.forEach(e,e=>{(0,a.isValidElement)(e)&&t.push(e)}),t}let w=({children:e,custom:t,initial:r=!0,onExitComplete:i,presenceAffectsLayout:s=!0,mode:o="sync",propagate:d=!1,anchorX:h="left"})=>{let[f,p]=(0,v.xQ)(d),m=(0,a.useMemo)(()=>b(e),[e]),y=d&&!f?[]:m.map(x),w=(0,a.useRef)(!0),j=(0,a.useRef)(m),P=(0,u.M)(()=>new Map),[T,E]=(0,a.useState)(m),[R,M]=(0,a.useState)(m);(0,c.E)(()=>{w.current=!1,j.current=m;for(let e=0;e<R.length;e++){let t=x(R[e]);y.includes(t)?P.delete(t):!0!==P.get(t)&&P.set(t,!1)}},[R,y.length,y.join("-")]);let C=[];if(m!==T){let e=[...m];for(let t=0;t<R.length;t++){let r=R[t],n=x(r);y.includes(n)||(e.splice(t,0,r),C.push(r))}return"wait"===o&&C.length&&(e=C),M(b(e)),E(m),null}let{forceRender:S}=(0,a.useContext)(l.L);return(0,n.jsx)(n.Fragment,{children:R.map(e=>{let a=x(e),l=(!d||!!f)&&(m===R||y.includes(a));return(0,n.jsx)(g,{isPresent:l,initial:(!w.current||!!r)&&void 0,custom:t,presenceAffectsLayout:s,mode:o,onExitComplete:l?void 0:()=>{if(!P.has(a))return;P.set(a,!0);let e=!0;P.forEach(t=>{t||(e=!1)}),e&&(S?.(),M(j.current),d&&p?.(),i&&i())},anchorX:h,children:e},a)})})};var j=r(6001),P=r(5814),T=r.n(P);let E=[{name:"Taxas ao Vivo",href:"#rates"},{name:"Comparar Plataformas",href:"#platforms"},{name:"Como Funciona",href:"#how-it-works"},{name:"Todas as Lojas",href:"#stores"}];function R(){let[e,t]=(0,a.useState)(!1),[r,l]=(0,a.useState)(!1),u=e=>{if(e.startsWith("#")){let t=document.querySelector(e);t&&t.scrollIntoView({behavior:"smooth"})}t(!1)};return(0,n.jsxs)("header",{className:`fixed left-0 right-0 top-0 z-50 transition-all duration-300 ${r?"glass shadow-lg backdrop-blur-md":"bg-transparent"}`,children:[(0,n.jsx)("nav",{className:"container-responsive","aria-label":"Global",children:(0,n.jsxs)("div",{className:"flex h-16 items-center justify-between lg:h-20",children:[(0,n.jsx)("div",{className:"flex lg:flex-1",children:(0,n.jsxs)(T(),{href:"/",className:"focus-visible -m-1.5 p-1.5",children:[(0,n.jsx)("span",{className:"sr-only",children:"CashBoost"}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"shadow-green-medium flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600 lg:h-10 lg:w-10",children:(0,n.jsx)("span",{className:"text-lg font-bold text-white lg:text-xl",children:"₵"})}),(0,n.jsx)("span",{className:"gradient-text font-poppins text-xl font-bold lg:text-2xl",children:"CashBoost"})]})]})}),(0,n.jsx)("div",{className:"hidden lg:flex lg:gap-x-8",children:E.map(e=>(0,n.jsx)("button",{onClick:()=>u(e.href),className:"focus-visible text-sm font-semibold leading-6 text-gray-700 transition-colors duration-200 hover:text-emerald-600",children:e.name},e.name))}),(0,n.jsxs)("div",{className:"hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4",children:[(0,n.jsxs)("button",{className:"btn-secondary text-sm",children:[(0,n.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Buscar Lojas"]}),(0,n.jsx)("button",{className:"btn-primary text-sm",children:"Comparar Taxas"})]}),(0,n.jsx)("div",{className:"flex lg:hidden",children:(0,n.jsxs)("button",{type:"button",className:"focus-visible -m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700 hover:text-emerald-600",onClick:()=>t(!0),children:[(0,n.jsx)("span",{className:"sr-only",children:"Open main menu"}),(0,n.jsx)(s,{className:"h-6 w-6","aria-hidden":"true"})]})})]})}),(0,n.jsx)(w,{children:e&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(j.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-40 bg-black/20 backdrop-blur-sm lg:hidden",onClick:()=>t(!1)}),(0,n.jsxs)(j.P.div,{initial:{x:"100%"},animate:{x:0},exit:{x:"100%"},transition:{type:"spring",damping:25,stiffness:200},className:"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10 lg:hidden",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("a",{href:"/",className:"-m-1.5 p-1.5",children:[(0,n.jsx)("span",{className:"sr-only",children:"CashBoost"}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"shadow-green-medium flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600",children:(0,n.jsx)("span",{className:"text-lg font-bold text-white",children:"₵"})}),(0,n.jsx)("span",{className:"gradient-text font-poppins text-xl font-bold",children:"CashBoost"})]})]}),(0,n.jsxs)("button",{type:"button",className:"focus-visible -m-2.5 rounded-md p-2.5 text-gray-700 hover:text-emerald-600",onClick:()=>t(!1),children:[(0,n.jsx)("span",{className:"sr-only",children:"Close menu"}),(0,n.jsx)(o,{className:"h-6 w-6","aria-hidden":"true"})]})]}),(0,n.jsx)("div",{className:"mt-6 flow-root",children:(0,n.jsxs)("div",{className:"-my-6 divide-y divide-gray-500/10",children:[(0,n.jsx)("div",{className:"space-y-2 py-6",children:E.map(e=>(0,n.jsx)("button",{onClick:()=>u(e.href),className:"focus-visible -mx-3 block w-full rounded-lg px-3 py-2 text-left text-base font-semibold leading-7 text-gray-900 transition-colors duration-200 hover:bg-emerald-50 hover:text-emerald-600",children:e.name},e.name))}),(0,n.jsxs)("div",{className:"space-y-4 py-6",children:[(0,n.jsxs)("button",{className:"btn-secondary w-full justify-center",onClick:()=>t(!1),children:[(0,n.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Buscar Lojas"]}),(0,n.jsx)("button",{className:"btn-primary w-full justify-center",onClick:()=>t(!1),children:"Comparar Taxas"})]})]})})]})]})})]})}},9435:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return a}});let n=r(642);function i(e){return void 0!==e}function a(e,t){var r,a;let s=null==(r=t.shouldScroll)||r,o=e.nextUrl;if(i(t.patchedTree)){let r=(0,n.computeChangedPath)(e.tree,t.patchedTree);r?o=r:o||(o=e.canonicalUrl)}return{canonicalUrl:i(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:i(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:i(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:i(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!s&&(!!i(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:s?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:s?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:i(t.patchedTree)?t.patchedTree:e.tree,nextUrl:o}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9651:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return c}});let n=r(7391),i=r(6770),a=r(2030),s=r(5232),o=r(6928),l=r(9435),u=r(9752);function c(e,t){let{serverResponse:{flightData:r,canonicalUrl:c},navigatedAt:d}=t,h={};if(h.preserveCustomHistoryState=!1,"string"==typeof r)return(0,s.handleExternalUrl)(e,h,r,e.pushRef.pendingPush);let f=e.tree,p=e.cache;for(let t of r){let{segmentPath:r,tree:l}=t,m=(0,i.applyRouterStatePatchToTree)(["",...r],f,l,e.canonicalUrl);if(null===m)return e;if((0,a.isNavigatingToNewRootLayout)(f,m))return(0,s.handleExternalUrl)(e,h,e.canonicalUrl,e.pushRef.pendingPush);let g=c?(0,n.createHrefFromUrl)(c):void 0;g&&(h.canonicalUrl=g);let y=(0,u.createEmptyCacheNode)();(0,o.applyFlightData)(d,p,y,t),h.patchedTree=m,h.cache=y,p=y,f=m}return(0,l.handleMutable)(e,h)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9707:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addSearchParamsToPageSegments:function(){return d},handleAliasedPrefetchEntry:function(){return c}});let n=r(3913),i=r(9752),a=r(6770),s=r(7391),o=r(3123),l=r(3898),u=r(9435);function c(e,t,r,c,h){let f,p=t.tree,m=t.cache,g=(0,s.createHrefFromUrl)(c);if("string"==typeof r)return!1;for(let t of r){if(!function e(t){if(!t)return!1;let r=t[2];if(t[3])return!0;for(let t in r)if(e(r[t]))return!0;return!1}(t.seedData))continue;let r=t.tree;r=d(r,Object.fromEntries(c.searchParams));let{seedData:s,isRootRender:u,pathToSegment:h}=t,y=["",...h];r=d(r,Object.fromEntries(c.searchParams));let v=(0,a.applyRouterStatePatchToTree)(y,p,r,g),x=(0,i.createEmptyCacheNode)();if(u&&s){let t=s[1];x.loading=s[3],x.rsc=t,function e(t,r,i,a,s){if(0!==Object.keys(a[1]).length)for(let l in a[1]){let u,c=a[1][l],d=c[0],h=(0,o.createRouterCacheKey)(d),f=null!==s&&void 0!==s[2][l]?s[2][l]:null;if(null!==f){let e=f[1],r=f[3];u={lazyData:null,rsc:d.includes(n.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else u={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let p=r.parallelRoutes.get(l);p?p.set(h,u):r.parallelRoutes.set(l,new Map([[h,u]])),e(t,u,i,c,f)}}(e,x,m,r,s)}else x.rsc=m.rsc,x.prefetchRsc=m.prefetchRsc,x.loading=m.loading,x.parallelRoutes=new Map(m.parallelRoutes),(0,l.fillCacheWithNewSubTreeDataButOnlyLoading)(e,x,m,t);v&&(p=v,m=x,f=!0)}return!!f&&(h.patchedTree=p,h.cache=m,h.canonicalUrl=g,h.hashFragment=c.hash,(0,u.handleMutable)(t,h))}function d(e,t){let[r,i,...a]=e;if(r.includes(n.PAGE_SEGMENT_KEY))return[(0,n.addSearchParamsIfPageSegment)(r,t),i,...a];let s={};for(let[e,r]of Object.entries(i))s[e]=d(r,t);return[r,s,...a]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9752:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return S},createPrefetchURL:function(){return M},default:function(){return _},isExternalURL:function(){return R}});let n=r(4441),i=r(687),a=n._(r(3210)),s=r(2142),o=r(9154),l=r(7391),u=r(449),c=r(9129),d=n._(r(5656)),h=r(5416),f=r(6127),p=r(7022),m=r(7086),g=r(4397),y=r(9330),v=r(5942),x=r(6736),b=r(642),w=r(2776),j=r(3690),P=r(6875),T=r(7860);r(3406);let E={};function R(e){return e.origin!==window.location.origin}function M(e){let t;if((0,h.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,f.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return R(t)?null:t}function C(e){let{appRouterState:t}=e;return(0,a.useInsertionEffect)(()=>{let{tree:e,pushRef:r,canonicalUrl:n}=t,i={...r.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};r.pendingPush&&(0,l.createHrefFromUrl)(new URL(window.location.href))!==n?(r.pendingPush=!1,window.history.pushState(i,"",n)):window.history.replaceState(i,"",n)},[t]),(0,a.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function S(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function A(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function N(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,i=null!==n?n:r;return(0,a.useDeferredValue)(r,i)}function k(e){let t,{actionQueue:r,assetPrefix:n,globalError:l}=e,h=(0,c.useActionQueue)(r),{canonicalUrl:f}=h,{searchParams:w,pathname:R}=(0,a.useMemo)(()=>{let e=new URL(f,"http://n");return{searchParams:e.searchParams,pathname:(0,x.hasBasePath)(e.pathname)?(0,v.removeBasePath)(e.pathname):e.pathname}},[f]);(0,a.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(E.pendingMpaPath=void 0,(0,c.dispatchAppRouterAction)({type:o.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,a.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,T.isRedirectError)(t)){e.preventDefault();let r=(0,P.getURLFromRedirectError)(t);(0,P.getRedirectTypeFromError)(t)===T.RedirectType.push?j.publicAppRouterInstance.push(r,{}):j.publicAppRouterInstance.replace(r,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:M}=h;if(M.mpaNavigation){if(E.pendingMpaPath!==f){let e=window.location;M.pendingPush?e.assign(f):e.replace(f),E.pendingMpaPath=f}(0,a.use)(y.unresolvedThenable)}(0,a.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,a.startTransition)(()=>{(0,c.dispatchAppRouterAction)({type:o.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,i){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=A(t),i&&r(i)),e(t,n,i)},window.history.replaceState=function(e,n,i){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=A(e),i&&r(i)),t(e,n,i)};let n=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,a.startTransition)(()=>{(0,j.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[]);let{cache:S,tree:k,nextUrl:_,focusAndScrollRef:O}=h,L=(0,a.useMemo)(()=>(0,g.findHeadInCache)(S,k[1]),[S,k]),V=(0,a.useMemo)(()=>(0,b.getSelectedParams)(k),[k]),U=(0,a.useMemo)(()=>({parentTree:k,parentCacheNode:S,parentSegmentPath:null,url:f}),[k,S,f]),I=(0,a.useMemo)(()=>({tree:k,focusAndScrollRef:O,nextUrl:_}),[k,O,_]);if(null!==L){let[e,r]=L;t=(0,i.jsx)(N,{headCacheNode:e},r)}else t=null;let B=(0,i.jsxs)(m.RedirectBoundary,{children:[t,S.rsc,(0,i.jsx)(p.AppRouterAnnouncer,{tree:k})]});return B=(0,i.jsx)(d.ErrorBoundary,{errorComponent:l[0],errorStyles:l[1],children:B}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(C,{appRouterState:h}),(0,i.jsx)(D,{}),(0,i.jsx)(u.PathParamsContext.Provider,{value:V,children:(0,i.jsx)(u.PathnameContext.Provider,{value:R,children:(0,i.jsx)(u.SearchParamsContext.Provider,{value:w,children:(0,i.jsx)(s.GlobalLayoutRouterContext.Provider,{value:I,children:(0,i.jsx)(s.AppRouterContext.Provider,{value:j.publicAppRouterInstance,children:(0,i.jsx)(s.LayoutRouterContext.Provider,{value:U,children:B})})})})})})]})}function _(e){let{actionQueue:t,globalErrorComponentAndStyles:[r,n],assetPrefix:a}=e;return(0,w.useNavFailureHandler)(),(0,i.jsx)(d.ErrorBoundary,{errorComponent:d.default,children:(0,i.jsx)(k,{actionQueue:t,assetPrefix:a,globalError:[r,n]})})}let O=new Set,L=new Set;function D(){let[,e]=a.default.useState(0),t=O.size;return(0,a.useEffect)(()=>{let r=()=>e(e=>e+1);return L.add(r),t!==O.size&&r(),()=>{L.delete(r)}},[t,e]),[...O].map((e,t)=>(0,i.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=O.size;return O.add(e),O.size!==t&&L.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9934:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx","default")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[808],()=>r(6469));module.exports=n})();