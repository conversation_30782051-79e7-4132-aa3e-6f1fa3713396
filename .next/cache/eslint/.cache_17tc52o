[{"/home/<USER>/workspace/novo/src/app/layout.tsx": "1", "/home/<USER>/workspace/novo/src/app/page.tsx": "2", "/home/<USER>/workspace/novo/src/components/CTASection.tsx": "3", "/home/<USER>/workspace/novo/src/components/CashbackRateCard.tsx": "4", "/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx": "5", "/home/<USER>/workspace/novo/src/components/Footer.tsx": "6", "/home/<USER>/workspace/novo/src/components/HeroSection.tsx": "7", "/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx": "8", "/home/<USER>/workspace/novo/src/components/OffersSection.tsx": "9", "/home/<USER>/workspace/novo/src/components/TopRatesSection.tsx": "10", "/home/<USER>/workspace/novo/src/components/TrustSection.tsx": "11", "/home/<USER>/workspace/novo/src/components/features/index.ts": "12", "/home/<USER>/workspace/novo/src/components/features/lazy.tsx": "13", "/home/<USER>/workspace/novo/src/components/features/performance/PerformanceDashboard.tsx": "14", "/home/<USER>/workspace/novo/src/components/features/rates/CashbackRateCard.tsx": "15", "/home/<USER>/workspace/novo/src/components/features/rates/TopRatesSection.tsx": "16", "/home/<USER>/workspace/novo/src/components/features/search/SearchBar.tsx": "17", "/home/<USER>/workspace/novo/src/components/layout/Container.tsx": "18", "/home/<USER>/workspace/novo/src/components/layout/Footer.tsx": "19", "/home/<USER>/workspace/novo/src/components/layout/Header.tsx": "20", "/home/<USER>/workspace/novo/src/components/layout/Section.tsx": "21", "/home/<USER>/workspace/novo/src/components/layout/index.ts": "22", "/home/<USER>/workspace/novo/src/components/ui/Announcer.tsx": "23", "/home/<USER>/workspace/novo/src/components/ui/Button.tsx": "24", "/home/<USER>/workspace/novo/src/components/ui/Card.tsx": "25", "/home/<USER>/workspace/novo/src/components/ui/ErrorBoundary.tsx": "26", "/home/<USER>/workspace/novo/src/components/ui/LazyLoad.tsx": "27", "/home/<USER>/workspace/novo/src/components/ui/Modal.tsx": "28", "/home/<USER>/workspace/novo/src/components/ui/Skeleton.tsx": "29", "/home/<USER>/workspace/novo/src/components/ui/SkipLink.tsx": "30", "/home/<USER>/workspace/novo/src/components/ui/ThemeToggle.tsx": "31", "/home/<USER>/workspace/novo/src/components/ui/Tooltip.tsx": "32", "/home/<USER>/workspace/novo/src/components/ui/__tests__/Button.test.tsx": "33", "/home/<USER>/workspace/novo/src/components/ui/index.ts": "34", "/home/<USER>/workspace/novo/src/constants/index.ts": "35", "/home/<USER>/workspace/novo/src/data/index.ts": "36", "/home/<USER>/workspace/novo/src/data/platforms.ts": "37", "/home/<USER>/workspace/novo/src/data/stores.ts": "38", "/home/<USER>/workspace/novo/src/hooks/__tests__/useDebounce.test.ts": "39", "/home/<USER>/workspace/novo/src/hooks/index.ts": "40", "/home/<USER>/workspace/novo/src/hooks/useCashbackData.ts": "41", "/home/<USER>/workspace/novo/src/hooks/useDebounce.ts": "42", "/home/<USER>/workspace/novo/src/hooks/useFocusManagement.ts": "43", "/home/<USER>/workspace/novo/src/hooks/useIntersectionObserver.ts": "44", "/home/<USER>/workspace/novo/src/hooks/useLocalStorage.ts": "45", "/home/<USER>/workspace/novo/src/hooks/useMediaQuery.ts": "46", "/home/<USER>/workspace/novo/src/hooks/usePerformance.ts": "47", "/home/<USER>/workspace/novo/src/hooks/useSearch.ts": "48", "/home/<USER>/workspace/novo/src/hooks/useTheme.ts": "49", "/home/<USER>/workspace/novo/src/lib/accessibility.ts": "50", "/home/<USER>/workspace/novo/src/lib/analytics.ts": "51", "/home/<USER>/workspace/novo/src/lib/index.ts": "52", "/home/<USER>/workspace/novo/src/lib/utils.ts": "53", "/home/<USER>/workspace/novo/src/types/index.ts": "54"}, {"size": 5405, "mtime": 1750468147868, "results": "55", "hashOfConfig": "56"}, {"size": 769, "mtime": 1750472428916, "results": "57", "hashOfConfig": "56"}, {"size": 9971, "mtime": 1750464070912, "results": "58", "hashOfConfig": "56"}, {"size": 5040, "mtime": 1750464850112, "results": "59", "hashOfConfig": "56"}, {"size": 6729, "mtime": 1750472103990, "results": "60", "hashOfConfig": "56"}, {"size": 12450, "mtime": 1750465683272, "results": "61", "hashOfConfig": "56"}, {"size": 8498, "mtime": 1750464794712, "results": "62", "hashOfConfig": "56"}, {"size": 8449, "mtime": 1750469631340, "results": "63", "hashOfConfig": "56"}, {"size": 11736, "mtime": 1750472172597, "results": "64", "hashOfConfig": "56"}, {"size": 9319, "mtime": 1750465117984, "results": "65", "hashOfConfig": "56"}, {"size": 11289, "mtime": 1750465572096, "results": "66", "hashOfConfig": "56"}, {"size": 364, "mtime": 1750469709853, "results": "67", "hashOfConfig": "56"}, {"size": 2645, "mtime": 1750469923911, "results": "68", "hashOfConfig": "56"}, {"size": 8448, "mtime": 1750471970338, "results": "69", "hashOfConfig": "56"}, {"size": 6343, "mtime": 1750471998601, "results": "70", "hashOfConfig": "56"}, {"size": 8493, "mtime": 1750472018519, "results": "71", "hashOfConfig": "56"}, {"size": 11168, "mtime": 1750471948665, "results": "72", "hashOfConfig": "56"}, {"size": 628, "mtime": 1750466453392, "results": "73", "hashOfConfig": "56"}, {"size": 9854, "mtime": 1750471927855, "results": "74", "hashOfConfig": "56"}, {"size": 6920, "mtime": 1750472417322, "results": "75", "hashOfConfig": "56"}, {"size": 1515, "mtime": 1750471530113, "results": "76", "hashOfConfig": "56"}, {"size": 172, "mtime": 1750470901836, "results": "77", "hashOfConfig": "56"}, {"size": 1818, "mtime": 1750467213676, "results": "78", "hashOfConfig": "56"}, {"size": 1939, "mtime": 1750466412848, "results": "79", "hashOfConfig": "56"}, {"size": 1712, "mtime": 1750466424948, "results": "80", "hashOfConfig": "56"}, {"size": 3475, "mtime": 1750466712112, "results": "81", "hashOfConfig": "56"}, {"size": 1968, "mtime": 1750466727032, "results": "82", "hashOfConfig": "56"}, {"size": 5951, "mtime": 1750467301139, "results": "83", "hashOfConfig": "56"}, {"size": 1253, "mtime": 1750466436924, "results": "84", "hashOfConfig": "56"}, {"size": 1260, "mtime": 1750467177724, "results": "85", "hashOfConfig": "56"}, {"size": 2081, "mtime": 1750466871148, "results": "86", "hashOfConfig": "56"}, {"size": 4423, "mtime": 1750467237868, "results": "87", "hashOfConfig": "56"}, {"size": 3425, "mtime": 1750467517701, "results": "88", "hashOfConfig": "56"}, {"size": 588, "mtime": 1750467314535, "results": "89", "hashOfConfig": "56"}, {"size": 1334, "mtime": 1750466241404, "results": "90", "hashOfConfig": "56"}, {"size": 131, "mtime": 1750466297236, "results": "91", "hashOfConfig": "56"}, {"size": 3877, "mtime": 1750466346912, "results": "92", "hashOfConfig": "56"}, {"size": 3742, "mtime": 1750466321068, "results": "93", "hashOfConfig": "56"}, {"size": 4488, "mtime": 1750467542807, "results": "94", "hashOfConfig": "56"}, {"size": 625, "mtime": 1750467330563, "results": "95", "hashOfConfig": "56"}, {"size": 5472, "mtime": 1750466376452, "results": "96", "hashOfConfig": "56"}, {"size": 465, "mtime": 1750466256636, "results": "97", "hashOfConfig": "56"}, {"size": 4329, "mtime": 1750467200360, "results": "98", "hashOfConfig": "56"}, {"size": 1177, "mtime": 1750466289924, "results": "99", "hashOfConfig": "56"}, {"size": 1287, "mtime": 1750466267756, "results": "100", "hashOfConfig": "56"}, {"size": 961, "mtime": 1750466277556, "results": "101", "hashOfConfig": "56"}, {"size": 4712, "mtime": 1750467004520, "results": "102", "hashOfConfig": "56"}, {"size": 1934, "mtime": 1750466391972, "results": "103", "hashOfConfig": "56"}, {"size": 2153, "mtime": 1750466855048, "results": "104", "hashOfConfig": "56"}, {"size": 5546, "mtime": 1750467270116, "results": "105", "hashOfConfig": "56"}, {"size": 7890, "mtime": 1750466953192, "results": "106", "hashOfConfig": "56"}, {"size": 128, "mtime": 1750466201320, "results": "107", "hashOfConfig": "56"}, {"size": 4252, "mtime": 1750466229612, "results": "108", "hashOfConfig": "56"}, {"size": 5841, "mtime": 1750462364785, "results": "109", "hashOfConfig": "56"}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "r880a7", {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/home/<USER>/workspace/novo/src/app/layout.tsx", [], [], "/home/<USER>/workspace/novo/src/app/page.tsx", [], [], "/home/<USER>/workspace/novo/src/components/CTASection.tsx", [], [], "/home/<USER>/workspace/novo/src/components/CashbackRateCard.tsx", [], [], "/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx", [], [], "/home/<USER>/workspace/novo/src/components/Footer.tsx", [], [], "/home/<USER>/workspace/novo/src/components/HeroSection.tsx", [], [], "/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx", [], [], "/home/<USER>/workspace/novo/src/components/OffersSection.tsx", [], [], "/home/<USER>/workspace/novo/src/components/TopRatesSection.tsx", ["272", "273", "274"], [], "/home/<USER>/workspace/novo/src/components/TrustSection.tsx", ["275", "276"], [], "/home/<USER>/workspace/novo/src/components/features/index.ts", [], [], "/home/<USER>/workspace/novo/src/components/features/lazy.tsx", ["277"], [], "/home/<USER>/workspace/novo/src/components/features/performance/PerformanceDashboard.tsx", ["278", "279", "280"], [], "/home/<USER>/workspace/novo/src/components/features/rates/CashbackRateCard.tsx", ["281", "282"], [], "/home/<USER>/workspace/novo/src/components/features/rates/TopRatesSection.tsx", [], [], "/home/<USER>/workspace/novo/src/components/features/search/SearchBar.tsx", ["283"], [], "/home/<USER>/workspace/novo/src/components/layout/Container.tsx", [], [], "/home/<USER>/workspace/novo/src/components/layout/Footer.tsx", [], [], "/home/<USER>/workspace/novo/src/components/layout/Header.tsx", ["284"], [], "/home/<USER>/workspace/novo/src/components/layout/Section.tsx", [], [], "/home/<USER>/workspace/novo/src/components/layout/index.ts", [], [], "/home/<USER>/workspace/novo/src/components/ui/Announcer.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/Button.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/Card.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/ErrorBoundary.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/LazyLoad.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/Modal.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/Skeleton.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/SkipLink.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/ThemeToggle.tsx", ["285"], [], "/home/<USER>/workspace/novo/src/components/ui/Tooltip.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/__tests__/Button.test.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/index.ts", [], [], "/home/<USER>/workspace/novo/src/constants/index.ts", [], [], "/home/<USER>/workspace/novo/src/data/index.ts", [], [], "/home/<USER>/workspace/novo/src/data/platforms.ts", [], [], "/home/<USER>/workspace/novo/src/data/stores.ts", [], [], "/home/<USER>/workspace/novo/src/hooks/__tests__/useDebounce.test.ts", [], [], "/home/<USER>/workspace/novo/src/hooks/index.ts", [], [], "/home/<USER>/workspace/novo/src/hooks/useCashbackData.ts", ["286", "287"], [], "/home/<USER>/workspace/novo/src/hooks/useDebounce.ts", [], [], "/home/<USER>/workspace/novo/src/hooks/useFocusManagement.ts", [], [], "/home/<USER>/workspace/novo/src/hooks/useIntersectionObserver.ts", [], [], "/home/<USER>/workspace/novo/src/hooks/useLocalStorage.ts", ["288"], [], "/home/<USER>/workspace/novo/src/hooks/useMediaQuery.ts", [], [], "/home/<USER>/workspace/novo/src/hooks/usePerformance.ts", ["289", "290", "291", "292", "293", "294"], [], "/home/<USER>/workspace/novo/src/hooks/useSearch.ts", [], [], "/home/<USER>/workspace/novo/src/hooks/useTheme.ts", [], [], "/home/<USER>/workspace/novo/src/lib/accessibility.ts", [], [], "/home/<USER>/workspace/novo/src/lib/analytics.ts", ["295", "296", "297", "298", "299", "300", "301", "302", "303", "304"], [], "/home/<USER>/workspace/novo/src/lib/index.ts", [], [], "/home/<USER>/workspace/novo/src/lib/utils.ts", ["305", "306"], [], "/home/<USER>/workspace/novo/src/types/index.ts", ["307"], [], {"ruleId": "308", "severity": 2, "message": "309", "line": 9, "column": 3, "nodeType": null, "messageId": "310", "endLine": 9, "endColumn": 11}, {"ruleId": "308", "severity": 2, "message": "311", "line": 13, "column": 22, "nodeType": null, "messageId": "310", "endLine": 13, "endColumn": 35}, {"ruleId": "308", "severity": 2, "message": "312", "line": 180, "column": 40, "nodeType": null, "messageId": "310", "endLine": 180, "endColumn": 52}, {"ruleId": "313", "severity": 2, "message": "314", "line": 246, "column": 19, "nodeType": "315", "messageId": "316", "suggestions": "317"}, {"ruleId": "313", "severity": 2, "message": "314", "line": 246, "column": 38, "nodeType": "315", "messageId": "316", "suggestions": "318"}, {"ruleId": "308", "severity": 2, "message": "319", "line": 2, "column": 10, "nodeType": null, "messageId": "310", "endLine": 2, "endColumn": 14}, {"ruleId": "320", "severity": 2, "message": "321", "line": 49, "column": 38, "nodeType": "322", "messageId": "323", "endLine": 49, "endColumn": 41, "suggestions": "324"}, {"ruleId": "320", "severity": 2, "message": "321", "line": 75, "column": 33, "nodeType": "322", "messageId": "323", "endLine": 75, "endColumn": 36, "suggestions": "325"}, {"ruleId": "320", "severity": 2, "message": "321", "line": 86, "column": 33, "nodeType": "322", "messageId": "323", "endLine": 86, "endColumn": 36, "suggestions": "326"}, {"ruleId": "308", "severity": 2, "message": "312", "line": 24, "column": 34, "nodeType": null, "messageId": "310", "endLine": 24, "endColumn": 46}, {"ruleId": "308", "severity": 2, "message": "327", "line": 70, "column": 30, "nodeType": null, "messageId": "310", "endLine": 70, "endColumn": 35}, {"ruleId": "320", "severity": 2, "message": "321", "line": 67, "column": 64, "nodeType": "322", "messageId": "323", "endLine": 67, "endColumn": 67, "suggestions": "328"}, {"ruleId": "329", "severity": 2, "message": "330", "line": 119, "column": 17, "nodeType": "331", "endLine": 119, "endColumn": 54}, {"ruleId": "320", "severity": 2, "message": "321", "line": 41, "column": 55, "nodeType": "322", "messageId": "323", "endLine": 41, "endColumn": 58, "suggestions": "332"}, {"ruleId": "308", "severity": 2, "message": "333", "line": 2, "column": 35, "nodeType": null, "messageId": "310", "endLine": 2, "endColumn": 40}, {"ruleId": "308", "severity": 2, "message": "334", "line": 70, "column": 17, "nodeType": null, "messageId": "310", "endLine": 70, "endColumn": 25}, {"ruleId": "308", "severity": 2, "message": "335", "line": 1, "column": 20, "nodeType": null, "messageId": "310", "endLine": 1, "endColumn": 29}, {"ruleId": "308", "severity": 2, "message": "336", "line": 4, "column": 11, "nodeType": null, "messageId": "310", "endLine": 4, "endColumn": 29}, {"ruleId": "337", "severity": 1, "message": "338", "line": 42, "column": 52, "nodeType": "339", "endLine": 42, "endColumn": 59}, {"ruleId": "320", "severity": 2, "message": "321", "line": 97, "column": 75, "nodeType": "322", "messageId": "323", "endLine": 97, "endColumn": 78, "suggestions": "340"}, {"ruleId": "337", "severity": 1, "message": "341", "line": 124, "column": 6, "nodeType": "339", "endLine": 124, "endColumn": 18}, {"ruleId": "337", "severity": 1, "message": "342", "line": 124, "column": 6, "nodeType": "339", "endLine": 124, "endColumn": 18, "suggestions": "343"}, {"ruleId": "308", "severity": 2, "message": "344", "line": 137, "column": 31, "nodeType": null, "messageId": "310", "endLine": 137, "endColumn": 35}, {"ruleId": "320", "severity": 2, "message": "321", "line": 5, "column": 22, "nodeType": "322", "messageId": "323", "endLine": 5, "endColumn": 25, "suggestions": "345"}, {"ruleId": "320", "severity": 2, "message": "321", "line": 6, "column": 17, "nodeType": "322", "messageId": "323", "endLine": 6, "endColumn": 20, "suggestions": "346"}, {"ruleId": "347", "severity": 2, "message": "348", "line": 41, "column": 30, "nodeType": "339", "messageId": "349", "endLine": 41, "endColumn": 39}, {"ruleId": "320", "severity": 2, "message": "321", "line": 105, "column": 57, "nodeType": "322", "messageId": "323", "endLine": 105, "endColumn": 60, "suggestions": "350"}, {"ruleId": "320", "severity": 2, "message": "321", "line": 191, "column": 31, "nodeType": "322", "messageId": "323", "endLine": 191, "endColumn": 34, "suggestions": "351"}, {"ruleId": "320", "severity": 2, "message": "321", "line": 210, "column": 31, "nodeType": "322", "messageId": "323", "endLine": 210, "endColumn": 34, "suggestions": "352"}, {"ruleId": "320", "severity": 2, "message": "321", "line": 233, "column": 40, "nodeType": "322", "messageId": "323", "endLine": 233, "endColumn": 43, "suggestions": "353"}, {"ruleId": "320", "severity": 2, "message": "321", "line": 252, "column": 56, "nodeType": "322", "messageId": "323", "endLine": 252, "endColumn": 59, "suggestions": "354"}, {"ruleId": "320", "severity": 2, "message": "321", "line": 273, "column": 71, "nodeType": "322", "messageId": "323", "endLine": 273, "endColumn": 74, "suggestions": "355"}, {"ruleId": "320", "severity": 2, "message": "321", "line": 278, "column": 54, "nodeType": "322", "messageId": "323", "endLine": 278, "endColumn": 57, "suggestions": "356"}, {"ruleId": "320", "severity": 2, "message": "321", "line": 48, "column": 46, "nodeType": "322", "messageId": "323", "endLine": 48, "endColumn": 49, "suggestions": "357"}, {"ruleId": "320", "severity": 2, "message": "321", "line": 48, "column": 56, "nodeType": "322", "messageId": "323", "endLine": 48, "endColumn": 59, "suggestions": "358"}, {"ruleId": "320", "severity": 2, "message": "321", "line": 272, "column": 31, "nodeType": "322", "messageId": "323", "endLine": 272, "endColumn": 34, "suggestions": "359"}, "@typescript-eslint/no-unused-vars", "'StarIcon' is defined but never used.", "unusedVar", "'StarIconSolid' is defined but never used.", "'trendPercent' is defined but never used.", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["360", "361", "362", "363"], ["364", "365", "366", "367"], "'lazy' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["368", "369"], ["370", "371"], ["372", "373"], "'rates' is assigned a value but never used.", ["374", "375"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", ["376", "377"], "'Store' is defined but never used.", "'setError' is assigned a value but never used.", "'useEffect' is defined but never used.", "'PerformanceMetrics' is defined but never used.", "react-hooks/exhaustive-deps", "The ref value 'mountTime.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'mountTime.current' to a variable inside the effect, and use that variable in the cleanup function.", "Identifier", ["378", "379"], "React Hook useEffect was passed a dependency list that is not an array literal. This means we can't statically verify whether you've passed the correct dependencies.", "React Hook useEffect has missing dependencies: 'componentName' and 'dependencies.length'. Either include them or remove the dependency array.", ["380"], "'type' is defined but never used.", ["381", "382"], ["383", "384"], "prefer-rest-params", "Use the rest parameters instead of 'arguments'.", "preferRestParams", ["385", "386"], ["387", "388"], ["389", "390"], ["391", "392"], ["393", "394"], ["395", "396"], ["397", "398"], ["399", "400"], ["401", "402"], ["403", "404"], {"messageId": "405", "data": "406", "fix": "407", "desc": "408"}, {"messageId": "405", "data": "409", "fix": "410", "desc": "411"}, {"messageId": "405", "data": "412", "fix": "413", "desc": "414"}, {"messageId": "405", "data": "415", "fix": "416", "desc": "417"}, {"messageId": "405", "data": "418", "fix": "419", "desc": "408"}, {"messageId": "405", "data": "420", "fix": "421", "desc": "411"}, {"messageId": "405", "data": "422", "fix": "423", "desc": "414"}, {"messageId": "405", "data": "424", "fix": "425", "desc": "417"}, {"messageId": "426", "fix": "427", "desc": "428"}, {"messageId": "429", "fix": "430", "desc": "431"}, {"messageId": "426", "fix": "432", "desc": "428"}, {"messageId": "429", "fix": "433", "desc": "431"}, {"messageId": "426", "fix": "434", "desc": "428"}, {"messageId": "429", "fix": "435", "desc": "431"}, {"messageId": "426", "fix": "436", "desc": "428"}, {"messageId": "429", "fix": "437", "desc": "431"}, {"messageId": "426", "fix": "438", "desc": "428"}, {"messageId": "429", "fix": "439", "desc": "431"}, {"messageId": "426", "fix": "440", "desc": "428"}, {"messageId": "429", "fix": "441", "desc": "431"}, {"desc": "442", "fix": "443"}, {"messageId": "426", "fix": "444", "desc": "428"}, {"messageId": "429", "fix": "445", "desc": "431"}, {"messageId": "426", "fix": "446", "desc": "428"}, {"messageId": "429", "fix": "447", "desc": "431"}, {"messageId": "426", "fix": "448", "desc": "428"}, {"messageId": "429", "fix": "449", "desc": "431"}, {"messageId": "426", "fix": "450", "desc": "428"}, {"messageId": "429", "fix": "451", "desc": "431"}, {"messageId": "426", "fix": "452", "desc": "428"}, {"messageId": "429", "fix": "453", "desc": "431"}, {"messageId": "426", "fix": "454", "desc": "428"}, {"messageId": "429", "fix": "455", "desc": "431"}, {"messageId": "426", "fix": "456", "desc": "428"}, {"messageId": "429", "fix": "457", "desc": "431"}, {"messageId": "426", "fix": "458", "desc": "428"}, {"messageId": "429", "fix": "459", "desc": "431"}, {"messageId": "426", "fix": "460", "desc": "428"}, {"messageId": "429", "fix": "461", "desc": "431"}, {"messageId": "426", "fix": "462", "desc": "428"}, {"messageId": "429", "fix": "463", "desc": "431"}, {"messageId": "426", "fix": "464", "desc": "428"}, {"messageId": "429", "fix": "465", "desc": "431"}, {"messageId": "426", "fix": "466", "desc": "428"}, {"messageId": "429", "fix": "467", "desc": "431"}, "replaceWithAlt", {"alt": "468"}, {"range": "469", "text": "470"}, "Replace with `&quot;`.", {"alt": "471"}, {"range": "472", "text": "473"}, "Replace with `&ldquo;`.", {"alt": "474"}, {"range": "475", "text": "476"}, "Replace with `&#34;`.", {"alt": "477"}, {"range": "478", "text": "479"}, "Replace with `&rdquo;`.", {"alt": "468"}, {"range": "480", "text": "481"}, {"alt": "471"}, {"range": "482", "text": "483"}, {"alt": "474"}, {"range": "484", "text": "485"}, {"alt": "477"}, {"range": "486", "text": "487"}, "suggestUnknown", {"range": "488", "text": "489"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "490", "text": "491"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "492", "text": "489"}, {"range": "493", "text": "491"}, {"range": "494", "text": "489"}, {"range": "495", "text": "491"}, {"range": "496", "text": "489"}, {"range": "497", "text": "491"}, {"range": "498", "text": "489"}, {"range": "499", "text": "491"}, {"range": "500", "text": "489"}, {"range": "501", "text": "491"}, "Update the dependencies array to be: [componentName, dependencies.length]", {"range": "502", "text": "503"}, {"range": "504", "text": "489"}, {"range": "505", "text": "491"}, {"range": "506", "text": "489"}, {"range": "507", "text": "491"}, {"range": "508", "text": "489"}, {"range": "509", "text": "491"}, {"range": "510", "text": "489"}, {"range": "511", "text": "491"}, {"range": "512", "text": "489"}, {"range": "513", "text": "491"}, {"range": "514", "text": "489"}, {"range": "515", "text": "491"}, {"range": "516", "text": "489"}, {"range": "517", "text": "491"}, {"range": "518", "text": "489"}, {"range": "519", "text": "491"}, {"range": "520", "text": "489"}, {"range": "521", "text": "491"}, {"range": "522", "text": "489"}, {"range": "523", "text": "491"}, {"range": "524", "text": "489"}, {"range": "525", "text": "491"}, {"range": "526", "text": "489"}, {"range": "527", "text": "491"}, "&quot;", [8487, 8507], "\n                  &quot;", "&ldquo;", [8487, 8507], "\n                  &ldquo;", "&#34;", [8487, 8507], "\n                  &#34;", "&rdquo;", [8487, 8507], "\n                  &rdquo;", [8525, 8543], "&quot;\n                ", [8525, 8543], "&ldquo;\n                ", [8525, 8543], "&#34;\n                ", [8525, 8543], "&rdquo;\n                ", [1654, 1657], "unknown", [1654, 1657], "never", [2479, 2482], [2479, 2482], [2873, 2876], [2873, 2876], [1813, 1816], [1813, 1816], [857, 860], [857, 860], [2684, 2687], [2684, 2687], [3481, 3493], "[componentName, dependencies.length]", [116, 119], [116, 119], [147, 150], [147, 150], [2594, 2597], [2594, 2597], [5136, 5139], [5136, 5139], [5848, 5851], [5848, 5851], [6410, 6413], [6410, 6413], [6883, 6886], [6883, 6886], [7512, 7515], [7512, 7515], [7843, 7846], [7843, 7846], [1231, 1234], [1231, 1234], [1241, 1244], [1241, 1244], [5687, 5690], [5687, 5690]]