[{"/home/<USER>/workspace/novo/src/app/layout.tsx": "1", "/home/<USER>/workspace/novo/src/app/page.tsx": "2", "/home/<USER>/workspace/novo/src/components/CTASection.tsx": "3", "/home/<USER>/workspace/novo/src/components/CashbackRateCard.tsx": "4", "/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx": "5", "/home/<USER>/workspace/novo/src/components/Footer.tsx": "6", "/home/<USER>/workspace/novo/src/components/HeroSection.tsx": "7", "/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx": "8", "/home/<USER>/workspace/novo/src/components/OffersSection.tsx": "9", "/home/<USER>/workspace/novo/src/components/TopRatesSection.tsx": "10", "/home/<USER>/workspace/novo/src/components/TrustSection.tsx": "11", "/home/<USER>/workspace/novo/src/components/features/index.ts": "12", "/home/<USER>/workspace/novo/src/components/features/performance/PerformanceDashboard.tsx": "13", "/home/<USER>/workspace/novo/src/components/features/rates/CashbackRateCard.tsx": "14", "/home/<USER>/workspace/novo/src/components/features/rates/TopRatesSection.tsx": "15", "/home/<USER>/workspace/novo/src/components/features/search/SearchBar.tsx": "16", "/home/<USER>/workspace/novo/src/components/layout/Container.tsx": "17", "/home/<USER>/workspace/novo/src/components/layout/Footer.tsx": "18", "/home/<USER>/workspace/novo/src/components/layout/Header.tsx": "19", "/home/<USER>/workspace/novo/src/components/layout/Section.tsx": "20", "/home/<USER>/workspace/novo/src/components/layout/index.ts": "21", "/home/<USER>/workspace/novo/src/components/ui/Announcer.tsx": "22", "/home/<USER>/workspace/novo/src/components/ui/Button.tsx": "23", "/home/<USER>/workspace/novo/src/components/ui/Card.tsx": "24", "/home/<USER>/workspace/novo/src/components/ui/ErrorBoundary.tsx": "25", "/home/<USER>/workspace/novo/src/components/ui/LazyLoad.tsx": "26", "/home/<USER>/workspace/novo/src/components/ui/Modal.tsx": "27", "/home/<USER>/workspace/novo/src/components/ui/Skeleton.tsx": "28", "/home/<USER>/workspace/novo/src/components/ui/SkipLink.tsx": "29", "/home/<USER>/workspace/novo/src/components/ui/ThemeToggle.tsx": "30", "/home/<USER>/workspace/novo/src/components/ui/Tooltip.tsx": "31", "/home/<USER>/workspace/novo/src/components/ui/__tests__/Button.test.tsx": "32", "/home/<USER>/workspace/novo/src/components/ui/index.ts": "33", "/home/<USER>/workspace/novo/src/constants/index.ts": "34", "/home/<USER>/workspace/novo/src/data/index.ts": "35", "/home/<USER>/workspace/novo/src/data/platforms.ts": "36", "/home/<USER>/workspace/novo/src/data/stores.ts": "37", "/home/<USER>/workspace/novo/src/hooks/__tests__/useDebounce.test.ts": "38", "/home/<USER>/workspace/novo/src/hooks/index.ts": "39", "/home/<USER>/workspace/novo/src/hooks/useCashbackData.ts": "40", "/home/<USER>/workspace/novo/src/hooks/useDebounce.ts": "41", "/home/<USER>/workspace/novo/src/hooks/useFocusManagement.ts": "42", "/home/<USER>/workspace/novo/src/hooks/useIntersectionObserver.ts": "43", "/home/<USER>/workspace/novo/src/hooks/useLocalStorage.ts": "44", "/home/<USER>/workspace/novo/src/hooks/useMediaQuery.ts": "45", "/home/<USER>/workspace/novo/src/hooks/usePerformance.ts": "46", "/home/<USER>/workspace/novo/src/hooks/useSearch.ts": "47", "/home/<USER>/workspace/novo/src/hooks/useTheme.ts": "48", "/home/<USER>/workspace/novo/src/lib/accessibility.ts": "49", "/home/<USER>/workspace/novo/src/lib/analytics.ts": "50", "/home/<USER>/workspace/novo/src/lib/index.ts": "51", "/home/<USER>/workspace/novo/src/lib/utils.ts": "52", "/home/<USER>/workspace/novo/src/types/index.ts": "53", "/home/<USER>/workspace/novo/src/components/features/lazy.tsx": "54"}, {"size": 5405, "mtime": 1750468147868, "results": "55", "hashOfConfig": "56"}, {"size": 785, "mtime": 1750471432474, "results": "57", "hashOfConfig": "56"}, {"size": 9971, "mtime": 1750464070912, "results": "58", "hashOfConfig": "56"}, {"size": 5040, "mtime": 1750464850112, "results": "59", "hashOfConfig": "56"}, {"size": 6827, "mtime": 1750464122928, "results": "60", "hashOfConfig": "56"}, {"size": 12450, "mtime": 1750465683272, "results": "61", "hashOfConfig": "56"}, {"size": 8498, "mtime": 1750464794712, "results": "62", "hashOfConfig": "56"}, {"size": 8449, "mtime": 1750469631340, "results": "63", "hashOfConfig": "56"}, {"size": 11342, "mtime": 1750462753261, "results": "64", "hashOfConfig": "56"}, {"size": 9319, "mtime": 1750465117984, "results": "65", "hashOfConfig": "56"}, {"size": 11289, "mtime": 1750465572096, "results": "66", "hashOfConfig": "56"}, {"size": 364, "mtime": 1750469709853, "results": "67", "hashOfConfig": "56"}, {"size": 8448, "mtime": 1750471970338, "results": "68", "hashOfConfig": "56"}, {"size": 6343, "mtime": 1750471998601, "results": "69", "hashOfConfig": "56"}, {"size": 8493, "mtime": 1750472018519, "results": "70", "hashOfConfig": "56"}, {"size": 11168, "mtime": 1750471948665, "results": "71", "hashOfConfig": "56"}, {"size": 628, "mtime": 1750466453392, "results": "72", "hashOfConfig": "56"}, {"size": 9854, "mtime": 1750471927855, "results": "73", "hashOfConfig": "56"}, {"size": 6936, "mtime": 1750471530113, "results": "74", "hashOfConfig": "56"}, {"size": 1515, "mtime": 1750471530113, "results": "75", "hashOfConfig": "56"}, {"size": 172, "mtime": 1750470901836, "results": "76", "hashOfConfig": "56"}, {"size": 1818, "mtime": 1750467213676, "results": "77", "hashOfConfig": "56"}, {"size": 1939, "mtime": 1750466412848, "results": "78", "hashOfConfig": "56"}, {"size": 1712, "mtime": 1750466424948, "results": "79", "hashOfConfig": "56"}, {"size": 3475, "mtime": 1750466712112, "results": "80", "hashOfConfig": "56"}, {"size": 1968, "mtime": 1750466727032, "results": "81", "hashOfConfig": "56"}, {"size": 5951, "mtime": 1750467301139, "results": "82", "hashOfConfig": "56"}, {"size": 1253, "mtime": 1750466436924, "results": "83", "hashOfConfig": "56"}, {"size": 1260, "mtime": 1750467177724, "results": "84", "hashOfConfig": "56"}, {"size": 2081, "mtime": 1750466871148, "results": "85", "hashOfConfig": "56"}, {"size": 4423, "mtime": 1750467237868, "results": "86", "hashOfConfig": "56"}, {"size": 3425, "mtime": 1750467517701, "results": "87", "hashOfConfig": "56"}, {"size": 588, "mtime": 1750467314535, "results": "88", "hashOfConfig": "56"}, {"size": 1334, "mtime": 1750466241404, "results": "89", "hashOfConfig": "56"}, {"size": 131, "mtime": 1750466297236, "results": "90", "hashOfConfig": "56"}, {"size": 3877, "mtime": 1750466346912, "results": "91", "hashOfConfig": "56"}, {"size": 3742, "mtime": 1750466321068, "results": "92", "hashOfConfig": "56"}, {"size": 4488, "mtime": 1750467542807, "results": "93", "hashOfConfig": "56"}, {"size": 625, "mtime": 1750467330563, "results": "94", "hashOfConfig": "56"}, {"size": 5472, "mtime": 1750466376452, "results": "95", "hashOfConfig": "56"}, {"size": 465, "mtime": 1750466256636, "results": "96", "hashOfConfig": "56"}, {"size": 4329, "mtime": 1750467200360, "results": "97", "hashOfConfig": "56"}, {"size": 1177, "mtime": 1750466289924, "results": "98", "hashOfConfig": "56"}, {"size": 1287, "mtime": 1750466267756, "results": "99", "hashOfConfig": "56"}, {"size": 961, "mtime": 1750466277556, "results": "100", "hashOfConfig": "56"}, {"size": 4712, "mtime": 1750467004520, "results": "101", "hashOfConfig": "56"}, {"size": 1934, "mtime": 1750466391972, "results": "102", "hashOfConfig": "56"}, {"size": 2153, "mtime": 1750466855048, "results": "103", "hashOfConfig": "56"}, {"size": 5546, "mtime": 1750467270116, "results": "104", "hashOfConfig": "56"}, {"size": 7890, "mtime": 1750466953192, "results": "105", "hashOfConfig": "56"}, {"size": 128, "mtime": 1750466201320, "results": "106", "hashOfConfig": "56"}, {"size": 4252, "mtime": 1750466229612, "results": "107", "hashOfConfig": "56"}, {"size": 5841, "mtime": 1750462364785, "results": "108", "hashOfConfig": "56"}, {"size": 2645, "mtime": 1750469923911, "results": "109", "hashOfConfig": "56"}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "r880a7", {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/home/<USER>/workspace/novo/src/app/layout.tsx", [], [], "/home/<USER>/workspace/novo/src/app/page.tsx", [], [], "/home/<USER>/workspace/novo/src/components/CTASection.tsx", [], [], "/home/<USER>/workspace/novo/src/components/CashbackRateCard.tsx", [], [], "/home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx", ["272"], [], "/home/<USER>/workspace/novo/src/components/Footer.tsx", [], [], "/home/<USER>/workspace/novo/src/components/HeroSection.tsx", [], [], "/home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx", [], [], "/home/<USER>/workspace/novo/src/components/OffersSection.tsx", ["273", "274", "275", "276"], [], "/home/<USER>/workspace/novo/src/components/TopRatesSection.tsx", ["277", "278", "279"], [], "/home/<USER>/workspace/novo/src/components/TrustSection.tsx", ["280", "281"], [], "/home/<USER>/workspace/novo/src/components/features/index.ts", [], [], "/home/<USER>/workspace/novo/src/components/features/performance/PerformanceDashboard.tsx", ["282", "283", "284"], [], "/home/<USER>/workspace/novo/src/components/features/rates/CashbackRateCard.tsx", ["285", "286"], [], "/home/<USER>/workspace/novo/src/components/features/rates/TopRatesSection.tsx", [], [], "/home/<USER>/workspace/novo/src/components/features/search/SearchBar.tsx", ["287"], [], "/home/<USER>/workspace/novo/src/components/layout/Container.tsx", [], [], "/home/<USER>/workspace/novo/src/components/layout/Footer.tsx", [], [], "/home/<USER>/workspace/novo/src/components/layout/Header.tsx", ["288"], [], "/home/<USER>/workspace/novo/src/components/layout/Section.tsx", [], [], "/home/<USER>/workspace/novo/src/components/layout/index.ts", [], [], "/home/<USER>/workspace/novo/src/components/ui/Announcer.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/Button.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/Card.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/ErrorBoundary.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/LazyLoad.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/Modal.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/Skeleton.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/SkipLink.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/ThemeToggle.tsx", ["289"], [], "/home/<USER>/workspace/novo/src/components/ui/Tooltip.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/__tests__/Button.test.tsx", [], [], "/home/<USER>/workspace/novo/src/components/ui/index.ts", [], [], "/home/<USER>/workspace/novo/src/constants/index.ts", [], [], "/home/<USER>/workspace/novo/src/data/index.ts", [], [], "/home/<USER>/workspace/novo/src/data/platforms.ts", [], [], "/home/<USER>/workspace/novo/src/data/stores.ts", [], [], "/home/<USER>/workspace/novo/src/hooks/__tests__/useDebounce.test.ts", [], [], "/home/<USER>/workspace/novo/src/hooks/index.ts", [], [], "/home/<USER>/workspace/novo/src/hooks/useCashbackData.ts", ["290", "291"], [], "/home/<USER>/workspace/novo/src/hooks/useDebounce.ts", [], [], "/home/<USER>/workspace/novo/src/hooks/useFocusManagement.ts", [], [], "/home/<USER>/workspace/novo/src/hooks/useIntersectionObserver.ts", [], [], "/home/<USER>/workspace/novo/src/hooks/useLocalStorage.ts", ["292"], [], "/home/<USER>/workspace/novo/src/hooks/useMediaQuery.ts", [], [], "/home/<USER>/workspace/novo/src/hooks/usePerformance.ts", ["293", "294", "295", "296", "297", "298"], [], "/home/<USER>/workspace/novo/src/hooks/useSearch.ts", [], [], "/home/<USER>/workspace/novo/src/hooks/useTheme.ts", [], [], "/home/<USER>/workspace/novo/src/lib/accessibility.ts", [], [], "/home/<USER>/workspace/novo/src/lib/analytics.ts", ["299", "300", "301", "302", "303", "304", "305", "306", "307", "308"], [], "/home/<USER>/workspace/novo/src/lib/index.ts", [], [], "/home/<USER>/workspace/novo/src/lib/utils.ts", ["309", "310"], [], "/home/<USER>/workspace/novo/src/types/index.ts", ["311"], [], "/home/<USER>/workspace/novo/src/components/features/lazy.tsx", ["312"], [], {"ruleId": "313", "severity": 2, "message": "314", "line": 4, "column": 8, "nodeType": null, "messageId": "315", "endLine": 4, "endColumn": 13}, {"ruleId": "313", "severity": 2, "message": "316", "line": 9, "column": 3, "nodeType": null, "messageId": "315", "endLine": 9, "endColumn": 11}, {"ruleId": "313", "severity": 2, "message": "317", "line": 11, "column": 22, "nodeType": null, "messageId": "315", "endLine": 11, "endColumn": 35}, {"ruleId": "313", "severity": 2, "message": "318", "line": 76, "column": 7, "nodeType": null, "messageId": "315", "endLine": 76, "endColumn": 17}, {"ruleId": "319", "severity": 2, "message": "320", "line": 132, "column": 16, "nodeType": "321", "messageId": "322", "suggestions": "323"}, {"ruleId": "313", "severity": 2, "message": "316", "line": 9, "column": 3, "nodeType": null, "messageId": "315", "endLine": 9, "endColumn": 11}, {"ruleId": "313", "severity": 2, "message": "317", "line": 13, "column": 22, "nodeType": null, "messageId": "315", "endLine": 13, "endColumn": 35}, {"ruleId": "313", "severity": 2, "message": "324", "line": 180, "column": 40, "nodeType": null, "messageId": "315", "endLine": 180, "endColumn": 52}, {"ruleId": "319", "severity": 2, "message": "325", "line": 246, "column": 19, "nodeType": "321", "messageId": "322", "suggestions": "326"}, {"ruleId": "319", "severity": 2, "message": "325", "line": 246, "column": 38, "nodeType": "321", "messageId": "322", "suggestions": "327"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 49, "column": 38, "nodeType": "330", "messageId": "331", "endLine": 49, "endColumn": 41, "suggestions": "332"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 75, "column": 33, "nodeType": "330", "messageId": "331", "endLine": 75, "endColumn": 36, "suggestions": "333"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 86, "column": 33, "nodeType": "330", "messageId": "331", "endLine": 86, "endColumn": 36, "suggestions": "334"}, {"ruleId": "313", "severity": 2, "message": "324", "line": 24, "column": 34, "nodeType": null, "messageId": "315", "endLine": 24, "endColumn": 46}, {"ruleId": "313", "severity": 2, "message": "335", "line": 70, "column": 30, "nodeType": null, "messageId": "315", "endLine": 70, "endColumn": 35}, {"ruleId": "328", "severity": 2, "message": "329", "line": 67, "column": 64, "nodeType": "330", "messageId": "331", "endLine": 67, "endColumn": 67, "suggestions": "336"}, {"ruleId": "337", "severity": 2, "message": "338", "line": 119, "column": 17, "nodeType": "339", "endLine": 119, "endColumn": 54}, {"ruleId": "328", "severity": 2, "message": "329", "line": 41, "column": 55, "nodeType": "330", "messageId": "331", "endLine": 41, "endColumn": 58, "suggestions": "340"}, {"ruleId": "313", "severity": 2, "message": "341", "line": 2, "column": 35, "nodeType": null, "messageId": "315", "endLine": 2, "endColumn": 40}, {"ruleId": "313", "severity": 2, "message": "342", "line": 70, "column": 17, "nodeType": null, "messageId": "315", "endLine": 70, "endColumn": 25}, {"ruleId": "313", "severity": 2, "message": "343", "line": 1, "column": 20, "nodeType": null, "messageId": "315", "endLine": 1, "endColumn": 29}, {"ruleId": "313", "severity": 2, "message": "344", "line": 4, "column": 11, "nodeType": null, "messageId": "315", "endLine": 4, "endColumn": 29}, {"ruleId": "345", "severity": 1, "message": "346", "line": 42, "column": 52, "nodeType": "347", "endLine": 42, "endColumn": 59}, {"ruleId": "328", "severity": 2, "message": "329", "line": 97, "column": 75, "nodeType": "330", "messageId": "331", "endLine": 97, "endColumn": 78, "suggestions": "348"}, {"ruleId": "345", "severity": 1, "message": "349", "line": 124, "column": 6, "nodeType": "347", "endLine": 124, "endColumn": 18}, {"ruleId": "345", "severity": 1, "message": "350", "line": 124, "column": 6, "nodeType": "347", "endLine": 124, "endColumn": 18, "suggestions": "351"}, {"ruleId": "313", "severity": 2, "message": "352", "line": 137, "column": 31, "nodeType": null, "messageId": "315", "endLine": 137, "endColumn": 35}, {"ruleId": "328", "severity": 2, "message": "329", "line": 5, "column": 22, "nodeType": "330", "messageId": "331", "endLine": 5, "endColumn": 25, "suggestions": "353"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 6, "column": 17, "nodeType": "330", "messageId": "331", "endLine": 6, "endColumn": 20, "suggestions": "354"}, {"ruleId": "355", "severity": 2, "message": "356", "line": 41, "column": 30, "nodeType": "347", "messageId": "357", "endLine": 41, "endColumn": 39}, {"ruleId": "328", "severity": 2, "message": "329", "line": 105, "column": 57, "nodeType": "330", "messageId": "331", "endLine": 105, "endColumn": 60, "suggestions": "358"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 191, "column": 31, "nodeType": "330", "messageId": "331", "endLine": 191, "endColumn": 34, "suggestions": "359"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 210, "column": 31, "nodeType": "330", "messageId": "331", "endLine": 210, "endColumn": 34, "suggestions": "360"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 233, "column": 40, "nodeType": "330", "messageId": "331", "endLine": 233, "endColumn": 43, "suggestions": "361"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 252, "column": 56, "nodeType": "330", "messageId": "331", "endLine": 252, "endColumn": 59, "suggestions": "362"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 273, "column": 71, "nodeType": "330", "messageId": "331", "endLine": 273, "endColumn": 74, "suggestions": "363"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 278, "column": 54, "nodeType": "330", "messageId": "331", "endLine": 278, "endColumn": 57, "suggestions": "364"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 48, "column": 46, "nodeType": "330", "messageId": "331", "endLine": 48, "endColumn": 49, "suggestions": "365"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 48, "column": 56, "nodeType": "330", "messageId": "331", "endLine": 48, "endColumn": 59, "suggestions": "366"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 272, "column": 31, "nodeType": "330", "messageId": "331", "endLine": 272, "endColumn": 34, "suggestions": "367"}, {"ruleId": "313", "severity": 2, "message": "368", "line": 2, "column": 10, "nodeType": null, "messageId": "315", "endLine": 2, "endColumn": 14}, "@typescript-eslint/no-unused-vars", "'Image' is defined but never used.", "unusedVar", "'StarIcon' is defined but never used.", "'StarIconSolid' is defined but never used.", "'categories' is assigned a value but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["369", "370", "371", "372"], "'trendPercent' is defined but never used.", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["373", "374", "375", "376"], ["377", "378", "379", "380"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["381", "382"], ["383", "384"], ["385", "386"], "'rates' is assigned a value but never used.", ["387", "388"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", ["389", "390"], "'Store' is defined but never used.", "'setError' is assigned a value but never used.", "'useEffect' is defined but never used.", "'PerformanceMetrics' is defined but never used.", "react-hooks/exhaustive-deps", "The ref value 'mountTime.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'mountTime.current' to a variable inside the effect, and use that variable in the cleanup function.", "Identifier", ["391", "392"], "React Hook useEffect was passed a dependency list that is not an array literal. This means we can't statically verify whether you've passed the correct dependencies.", "React Hook useEffect has missing dependencies: 'componentName' and 'dependencies.length'. Either include them or remove the dependency array.", ["393"], "'type' is defined but never used.", ["394", "395"], ["396", "397"], "prefer-rest-params", "Use the rest parameters instead of 'arguments'.", "preferRestParams", ["398", "399"], ["400", "401"], ["402", "403"], ["404", "405"], ["406", "407"], ["408", "409"], ["410", "411"], ["412", "413"], ["414", "415"], ["416", "417"], "'lazy' is defined but never used.", {"messageId": "418", "data": "419", "fix": "420", "desc": "421"}, {"messageId": "418", "data": "422", "fix": "423", "desc": "424"}, {"messageId": "418", "data": "425", "fix": "426", "desc": "427"}, {"messageId": "418", "data": "428", "fix": "429", "desc": "430"}, {"messageId": "418", "data": "431", "fix": "432", "desc": "433"}, {"messageId": "418", "data": "434", "fix": "435", "desc": "436"}, {"messageId": "418", "data": "437", "fix": "438", "desc": "439"}, {"messageId": "418", "data": "440", "fix": "441", "desc": "442"}, {"messageId": "418", "data": "443", "fix": "444", "desc": "433"}, {"messageId": "418", "data": "445", "fix": "446", "desc": "436"}, {"messageId": "418", "data": "447", "fix": "448", "desc": "439"}, {"messageId": "418", "data": "449", "fix": "450", "desc": "442"}, {"messageId": "451", "fix": "452", "desc": "453"}, {"messageId": "454", "fix": "455", "desc": "456"}, {"messageId": "451", "fix": "457", "desc": "453"}, {"messageId": "454", "fix": "458", "desc": "456"}, {"messageId": "451", "fix": "459", "desc": "453"}, {"messageId": "454", "fix": "460", "desc": "456"}, {"messageId": "451", "fix": "461", "desc": "453"}, {"messageId": "454", "fix": "462", "desc": "456"}, {"messageId": "451", "fix": "463", "desc": "453"}, {"messageId": "454", "fix": "464", "desc": "456"}, {"messageId": "451", "fix": "465", "desc": "453"}, {"messageId": "454", "fix": "466", "desc": "456"}, {"desc": "467", "fix": "468"}, {"messageId": "451", "fix": "469", "desc": "453"}, {"messageId": "454", "fix": "470", "desc": "456"}, {"messageId": "451", "fix": "471", "desc": "453"}, {"messageId": "454", "fix": "472", "desc": "456"}, {"messageId": "451", "fix": "473", "desc": "453"}, {"messageId": "454", "fix": "474", "desc": "456"}, {"messageId": "451", "fix": "475", "desc": "453"}, {"messageId": "454", "fix": "476", "desc": "456"}, {"messageId": "451", "fix": "477", "desc": "453"}, {"messageId": "454", "fix": "478", "desc": "456"}, {"messageId": "451", "fix": "479", "desc": "453"}, {"messageId": "454", "fix": "480", "desc": "456"}, {"messageId": "451", "fix": "481", "desc": "453"}, {"messageId": "454", "fix": "482", "desc": "456"}, {"messageId": "451", "fix": "483", "desc": "453"}, {"messageId": "454", "fix": "484", "desc": "456"}, {"messageId": "451", "fix": "485", "desc": "453"}, {"messageId": "454", "fix": "486", "desc": "456"}, {"messageId": "451", "fix": "487", "desc": "453"}, {"messageId": "454", "fix": "488", "desc": "456"}, {"messageId": "451", "fix": "489", "desc": "453"}, {"messageId": "454", "fix": "490", "desc": "456"}, {"messageId": "451", "fix": "491", "desc": "453"}, {"messageId": "454", "fix": "492", "desc": "456"}, "replaceWithAlt", {"alt": "493"}, {"range": "494", "text": "495"}, "Replace with `&apos;`.", {"alt": "496"}, {"range": "497", "text": "498"}, "Replace with `&lsquo;`.", {"alt": "499"}, {"range": "500", "text": "501"}, "Replace with `&#39;`.", {"alt": "502"}, {"range": "503", "text": "504"}, "Replace with `&rsquo;`.", {"alt": "505"}, {"range": "506", "text": "507"}, "Replace with `&quot;`.", {"alt": "508"}, {"range": "509", "text": "510"}, "Replace with `&ldquo;`.", {"alt": "511"}, {"range": "512", "text": "513"}, "Replace with `&#34;`.", {"alt": "514"}, {"range": "515", "text": "516"}, "Replace with `&rdquo;`.", {"alt": "505"}, {"range": "517", "text": "518"}, {"alt": "508"}, {"range": "519", "text": "520"}, {"alt": "511"}, {"range": "521", "text": "522"}, {"alt": "514"}, {"range": "523", "text": "524"}, "suggestUnknown", {"range": "525", "text": "526"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "527", "text": "528"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "529", "text": "526"}, {"range": "530", "text": "528"}, {"range": "531", "text": "526"}, {"range": "532", "text": "528"}, {"range": "533", "text": "526"}, {"range": "534", "text": "528"}, {"range": "535", "text": "526"}, {"range": "536", "text": "528"}, {"range": "537", "text": "526"}, {"range": "538", "text": "528"}, "Update the dependencies array to be: [componentName, dependencies.length]", {"range": "539", "text": "540"}, {"range": "541", "text": "526"}, {"range": "542", "text": "528"}, {"range": "543", "text": "526"}, {"range": "544", "text": "528"}, {"range": "545", "text": "526"}, {"range": "546", "text": "528"}, {"range": "547", "text": "526"}, {"range": "548", "text": "528"}, {"range": "549", "text": "526"}, {"range": "550", "text": "528"}, {"range": "551", "text": "526"}, {"range": "552", "text": "528"}, {"range": "553", "text": "526"}, {"range": "554", "text": "528"}, {"range": "555", "text": "526"}, {"range": "556", "text": "528"}, {"range": "557", "text": "526"}, {"range": "558", "text": "528"}, {"range": "559", "text": "526"}, {"range": "560", "text": "528"}, {"range": "561", "text": "526"}, {"range": "562", "text": "528"}, {"range": "563", "text": "526"}, {"range": "564", "text": "528"}, "&apos;", [4092, 4238], "\n            Don&apos;t miss these time-sensitive offers! Get extra cashback on top of regular rates \n            from your favorite stores.\n          ", "&lsquo;", [4092, 4238], "\n            Don&lsquo;t miss these time-sensitive offers! Get extra cashback on top of regular rates \n            from your favorite stores.\n          ", "&#39;", [4092, 4238], "\n            Don&#39;t miss these time-sensitive offers! Get extra cashback on top of regular rates \n            from your favorite stores.\n          ", "&rsquo;", [4092, 4238], "\n            Don&rsquo;t miss these time-sensitive offers! Get extra cashback on top of regular rates \n            from your favorite stores.\n          ", "&quot;", [8487, 8507], "\n                  &quot;", "&ldquo;", [8487, 8507], "\n                  &ldquo;", "&#34;", [8487, 8507], "\n                  &#34;", "&rdquo;", [8487, 8507], "\n                  &rdquo;", [8525, 8543], "&quot;\n                ", [8525, 8543], "&ldquo;\n                ", [8525, 8543], "&#34;\n                ", [8525, 8543], "&rdquo;\n                ", [1654, 1657], "unknown", [1654, 1657], "never", [2479, 2482], [2479, 2482], [2873, 2876], [2873, 2876], [1813, 1816], [1813, 1816], [857, 860], [857, 860], [2684, 2687], [2684, 2687], [3481, 3493], "[componentName, dependencies.length]", [116, 119], [116, 119], [147, 150], [147, 150], [2594, 2597], [2594, 2597], [5136, 5139], [5136, 5139], [5848, 5851], [5848, 5851], [6410, 6413], [6410, 6413], [6883, 6886], [6883, 6886], [7512, 7515], [7512, 7515], [7843, 7846], [7843, 7846], [1231, 1234], [1231, 1234], [1241, 1244], [1241, 1244], [5687, 5690], [5687, 5690]]